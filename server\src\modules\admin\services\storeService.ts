import prisma from '@/config/prismaClient';
import { StoreItemStatus } from '@prisma/client';

interface StoreItemFilters {
  category?: string;
  status?: string;
  search?: string;
}

interface CreateStoreItemData {
  name: string;
  description: string;
  price: number;
  coinPrice: number;
  quantity: number;
  category: string;
  image?: string | null;
}

interface UpdateStoreItemData {
  name?: string;
  description?: string;
  price?: number;
  coinPrice?: number;
  quantity?: number;
  category?: string;
  image?: string;
  status?: StoreItemStatus;
}

export const getAllStoreItems = async (filters: StoreItemFilters) => {
  const where: any = {};

  if (filters.category) {
    where.category = filters.category;
  }

  if (filters.status) {
    where.status = filters.status.toUpperCase() as StoreItemStatus;
  }

  if (filters.search) {
    where.OR = [
      {
        name: {
          contains: filters.search,
          mode: 'insensitive'
        }
      },
      {
        description: {
          contains: filters.search,
          mode: 'insensitive'
        }
      }
    ];
  }

  return await prisma.storeItem.findMany({
    where,
    orderBy: {
      createdAt: 'desc'
    }
  });
};

export const getStoreItemById = async (id: string) => {
  return await prisma.storeItem.findUnique({
    where: { id }
  });
};

export const createStoreItem = async (data: CreateStoreItemData) => {
  const status = data.quantity > 0 ? StoreItemStatus.ACTIVE : StoreItemStatus.INACTIVE;
  
  return await prisma.storeItem.create({
    data: {
      ...data,
      status
    }
  });
};

export const updateStoreItem = async (id: string, data: UpdateStoreItemData) => {
  const updateData: any = { ...data };
  
  // Auto-update status based on quantity if quantity is being updated
  if (data.quantity !== undefined) {
    updateData.status = data.quantity > 0 ? StoreItemStatus.ACTIVE : StoreItemStatus.INACTIVE;
  }
  
  return await prisma.storeItem.update({
    where: { id },
    data: updateData
  });
};

export const deleteStoreItem = async (id: string) => {
  return await prisma.storeItem.delete({
    where: { id }
  });
};

export const getStoreStats = async () => {
  const [
    totalItems,
    activeItems,
    inactiveItems,
    outOfStockItems,
    categories
  ] = await Promise.all([
    prisma.storeItem.count(),
    prisma.storeItem.count({
      where: { status: StoreItemStatus.ACTIVE }
    }),
    prisma.storeItem.count({
      where: { status: StoreItemStatus.INACTIVE }
    }),
    prisma.storeItem.count({
      where: { quantity: 0 }
    }),
    prisma.storeItem.groupBy({
      by: ['category'],
      _count: {
        category: true
      }
    })
  ]);

  return {
    totalItems,
    activeItems,
    inactiveItems,
    outOfStockItems,
    categoriesCount: categories.length,
    categories: categories.map(cat => ({
      category: cat.category,
      count: cat._count.category
    }))
  };
};
