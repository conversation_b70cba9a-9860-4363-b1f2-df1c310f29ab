<?php

namespace Classroom\Models;

use Department\Models\Department;
use Fees\Models\ClassroomFeesDetails;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Classroom extends Model
{
    use HasFactory;

    public $table = 'classrooms';

    protected $fillable = [
        'class_name',
        'department_id',
        "year_id",
        'class_uuid',
    ];


    public function department_name()
    {
        return $this->belongsTo(Department::class,'department_id', 'id');
    }

    public function feesDetails()
    {
        return $this->hasMany(ClassroomFeesDetails::class, 'classroom_id');
    }
}
