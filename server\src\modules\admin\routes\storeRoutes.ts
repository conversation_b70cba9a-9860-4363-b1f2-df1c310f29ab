import { Router } from 'express';
import * as storeController from '../controllers/storeController';
import { authMiddleware } from '@/middlewares/adminAuth';

const router = Router();

// Test route
router.get('/test', (req, res) => {
  res.json({ message: 'Store API is working!' });
});

// GET /api/admin/store - Get all store items with optional filters
router.get('/', storeController.getAllStoreItems);

// GET /api/admin/store/stats - Get store statistics
router.get('/stats', storeController.getStoreStats);

// GET /api/admin/store/:id - Get store item by ID
router.get('/:id', storeController.getStoreItemById);

// POST /api/admin/store - Create new store item
router.post('/', authMiddleware, storeController.createStoreItem);

// PUT /api/admin/store/:id - Update store item
router.put('/:id', authMiddleware, storeController.updateStoreItem);

// DELETE /api/admin/store/:id - Delete store item
router.delete('/:id', authMiddleware, storeController.deleteStoreItem);

export default router;
