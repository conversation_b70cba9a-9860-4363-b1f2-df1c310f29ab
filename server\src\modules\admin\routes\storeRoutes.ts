import { Router } from 'express';
import * as storeController from '../controllers/storeController';
import { authenticateAdmin } from '../middelware/authMiddleware';

const router = Router();

// Apply admin authentication middleware to all routes
router.use(authenticateAdmin);

// GET /api/admin/store - Get all store items with optional filters
router.get('/', storeController.getAllStoreItems);

// GET /api/admin/store/stats - Get store statistics
router.get('/stats', storeController.getStoreStats);

// GET /api/admin/store/:id - Get store item by ID
router.get('/:id', storeController.getStoreItemById);

// POST /api/admin/store - Create new store item
router.post('/', storeController.createStoreItem);

// PUT /api/admin/store/:id - Update store item
router.put('/:id', storeController.updateStoreItem);

// DELETE /api/admin/store/:id - Delete store item
router.delete('/:id', storeController.deleteStoreItem);

export default router;
