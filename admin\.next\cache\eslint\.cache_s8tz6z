[{"G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\blog\\page.tsx": "1", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\blog\\[id]\\page.tsx": "2", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-details\\[id]\\page.tsx": "3", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-thoughts\\page.tsx": "4", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\dashboard\\data-table.tsx": "5", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\dashboard\\page.tsx": "6", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\layout.tsx": "7", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\reviews\\page.tsx": "8", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\testimonials\\page.tsx": "9", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-detail\\page.tsx": "10", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-detail\\result\\[examId]\\page.tsx": "11", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\layout.tsx": "12", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\quiz-termination-log\\page.tsx": "13", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\examSlice.ts": "14", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\layout.tsx": "15", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\login\\page.tsx": "16", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\page.tsx": "17", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\ReduxProvider.tsx": "18", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\store.ts": "19", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\app-sidebar.tsx": "20", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\dataTable.tsx": "21", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\nav-main.tsx": "22", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\nav-user.tsx": "23", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\site-header.tsx": "24", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ConfirmationDialog.tsx": "25", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ExamForm.tsx": "26", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ExamTable.tsx": "27", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\TestimonialTable.tsx": "28", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\accordion.tsx": "29", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\alert-dialog.tsx": "30", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\avatar.tsx": "31", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\badge.tsx": "32", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\button.tsx": "33", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\card.tsx": "34", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\chart.tsx": "35", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\checkbox.tsx": "36", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\dialog.tsx": "37", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\dropdown-menu.tsx": "38", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\form.tsx": "39", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\input.tsx": "40", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\label.tsx": "41", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\select.tsx": "42", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\separator.tsx": "43", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sheet.tsx": "44", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sidebar.tsx": "45", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\skeleton.tsx": "46", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sonner.tsx": "47", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\table.tsx": "48", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\tabs.tsx": "49", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\toggle-group.tsx": "50", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\toggle.tsx": "51", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\tooltip.tsx": "52", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\hooks\\use-mobile.ts": "53", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\axios.ts": "54", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\types.ts": "55", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\utils.ts": "56", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\examSchema.ts": "57", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\auth.ts": "58", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\blogApi.ts": "59", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classesApi.ts": "60", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classesThoughtApi.ts": "61", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\email.ts": "62", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examApi.ts": "63", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examApplicationApi.ts": "64", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\questionApi.ts": "65", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\quizTerminationLog.ts": "66", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\reviewsApi.ts": "67", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-details\\page.tsx": "68", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\studentApi.ts": "69", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\question-bank\\page.tsx": "70", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\questionBankApi.ts": "71", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\testimonialApi.ts": "72", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\rank-price\\[examId]\\page.tsx": "73", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\PriceRankTable.tsx": "74", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\priceRankSchema.ts": "75", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizPriceRankApi.ts": "76", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-profile\\[id]\\page.tsx": "77", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-applicant\\[examId]\\page.tsx": "78", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\StudentProfileModal.tsx": "79", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizExamApplicantApi.ts": "80", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\page.tsx": "81", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\ReferralFilters.tsx": "82", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\[linkId]\\referred-users\\page.tsx": "83", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\calendar.tsx": "84", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\not-found.tsx": "85", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\chat\\page.tsx": "86", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddCertificateForm.tsx": "87", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddEducationForm.tsx": "88", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddExperienceForm.tsx": "89", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\CertificateTab.tsx": "90", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\EducationTab.tsx": "91", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\ImagesTab.tsx": "92", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\ProfileTab.tsx": "93", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionClassForm.tsx": "94", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionClassList.tsx": "95", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionTab.tsx": "96", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\WorkTab.tsx": "97", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\page.tsx": "98", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-edit\\[id]\\page.tsx": "99", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\AdminChat.tsx": "100", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\multi-select.tsx": "101", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\classesEditSchema.ts": "102", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\chatApi.ts": "103", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-prefrence\\[examId]\\page.tsx": "104", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\uwhiz-terminated-students\\[examId]\\page.tsx": "105", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\LevelPrenceManager.tsx": "106", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\SubjectPreferenceManager.tsx": "107", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizLevelPrefrenceApi.ts": "108", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizQuizTerminationLogApi.ts": "109", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizSubjectPrefrenceApi.ts": "110", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-monitoring\\page.tsx": "111", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\StudentPhotoMonitoring.tsx": "112", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examMonitoringApi.ts": "113", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\uwhiz-result\\[examId]\\page.tsx": "114", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\notifications\\page.tsx": "115", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\AdminNotificationBell.tsx": "116", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\notificationService.ts": "117", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-student\\[id]\\page.tsx": "118", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\detail\\[detailId]\\page.tsx": "119", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\detail\\[detailId]\\sub-detail\\[subDetailId]\\page.tsx": "120", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\page.tsx": "121", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\page.tsx": "122", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\mock-question-bank\\page.tsx": "123", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\ConfirmDialog.tsx": "124", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\pagination.tsx": "125", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classes-student.ts": "126", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\constantsApi.ts": "127", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\mock-examApi.ts": "128", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\activity-logs\\page.tsx": "129", "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\activityLogApi.ts": "130"}, {"size": 6958, "mtime": *************, "results": "131", "hashOfConfig": "132"}, {"size": 6736, "mtime": *************, "results": "133", "hashOfConfig": "132"}, {"size": 27265, "mtime": *************, "results": "134", "hashOfConfig": "132"}, {"size": 8414, "mtime": *************, "results": "135", "hashOfConfig": "132"}, {"size": 14092, "mtime": *************, "results": "136", "hashOfConfig": "132"}, {"size": 144, "mtime": 1747109266160, "results": "137", "hashOfConfig": "132"}, {"size": 698, "mtime": 1747109266162, "results": "138", "hashOfConfig": "132"}, {"size": 4377, "mtime": 1752465853423, "results": "139", "hashOfConfig": "132"}, {"size": 248, "mtime": 1747650902107, "results": "140", "hashOfConfig": "132"}, {"size": 272, "mtime": 1747818545268, "results": "141", "hashOfConfig": "132"}, {"size": 1666, "mtime": 1752465855077, "results": "142", "hashOfConfig": "132"}, {"size": 698, "mtime": 1747109266172, "results": "143", "hashOfConfig": "132"}, {"size": 4900, "mtime": 1747109266327, "results": "144", "hashOfConfig": "132"}, {"size": 6769, "mtime": 1747624459637, "results": "145", "hashOfConfig": "132"}, {"size": 587, "mtime": 1747109266338, "results": "146", "hashOfConfig": "132"}, {"size": 3150, "mtime": 1747109266339, "results": "147", "hashOfConfig": "132"}, {"size": 111, "mtime": 1747109266341, "results": "148", "hashOfConfig": "132"}, {"size": 236, "mtime": 1747109266328, "results": "149", "hashOfConfig": "132"}, {"size": 325, "mtime": 1747109266342, "results": "150", "hashOfConfig": "132"}, {"size": 3618, "mtime": 1753182055459, "results": "151", "hashOfConfig": "132"}, {"size": 2911, "mtime": 1752465850414, "results": "152", "hashOfConfig": "132"}, {"size": 3329, "mtime": 1747797160821, "results": "153", "hashOfConfig": "132"}, {"size": 3210, "mtime": 1747109266135, "results": "154", "hashOfConfig": "132"}, {"size": 964, "mtime": 1751625230881, "results": "155", "hashOfConfig": "132"}, {"size": 1292, "mtime": 1751276652287, "results": "156", "hashOfConfig": "132"}, {"size": 15071, "mtime": 1747797160859, "results": "157", "hashOfConfig": "132"}, {"size": 8679, "mtime": 1752465855843, "results": "158", "hashOfConfig": "132"}, {"size": 10610, "mtime": 1752465856725, "results": "159", "hashOfConfig": "132"}, {"size": 2125, "mtime": 1747109266363, "results": "160", "hashOfConfig": "132"}, {"size": 4021, "mtime": 1747289688502, "results": "161", "hashOfConfig": "132"}, {"size": 1090, "mtime": 1747109266365, "results": "162", "hashOfConfig": "132"}, {"size": 1634, "mtime": 1747109266367, "results": "163", "hashOfConfig": "132"}, {"size": 2158, "mtime": 1747109266369, "results": "164", "hashOfConfig": "132"}, {"size": 2003, "mtime": 1747109266374, "results": "165", "hashOfConfig": "132"}, {"size": 10019, "mtime": 1747109266376, "results": "166", "hashOfConfig": "132"}, {"size": 1258, "mtime": 1747109266385, "results": "167", "hashOfConfig": "132"}, {"size": 3915, "mtime": 1748363529403, "results": "168", "hashOfConfig": "132"}, {"size": 8407, "mtime": 1747109266407, "results": "169", "hashOfConfig": "132"}, {"size": 3871, "mtime": 1747109266409, "results": "170", "hashOfConfig": "132"}, {"size": 997, "mtime": 1750649653890, "results": "171", "hashOfConfig": "132"}, {"size": 639, "mtime": 1752465856736, "results": "172", "hashOfConfig": "132"}, {"size": 6433, "mtime": 1750649653892, "results": "173", "hashOfConfig": "132"}, {"size": 738, "mtime": 1747109266416, "results": "174", "hashOfConfig": "132"}, {"size": 4227, "mtime": 1747109266425, "results": "175", "hashOfConfig": "132"}, {"size": 22330, "mtime": 1747109266426, "results": "176", "hashOfConfig": "132"}, {"size": 292, "mtime": 1747109266428, "results": "177", "hashOfConfig": "132"}, {"size": 596, "mtime": 1747109266429, "results": "178", "hashOfConfig": "132"}, {"size": 2458, "mtime": 1747109266431, "results": "179", "hashOfConfig": "132"}, {"size": 2016, "mtime": 1747109266432, "results": "180", "hashOfConfig": "132"}, {"size": 1997, "mtime": 1747109266434, "results": "181", "hashOfConfig": "132"}, {"size": 1622, "mtime": 1747109266437, "results": "182", "hashOfConfig": "132"}, {"size": 1953, "mtime": 1747109266453, "results": "183", "hashOfConfig": "132"}, {"size": 595, "mtime": 1747109266455, "results": "184", "hashOfConfig": "132"}, {"size": 1155, "mtime": 1748962020698, "results": "185", "hashOfConfig": "132"}, {"size": 13360, "mtime": 1753184694730, "results": "186", "hashOfConfig": "132"}, {"size": 407, "mtime": 1747289688590, "results": "187", "hashOfConfig": "132"}, {"size": 2333, "mtime": 1750649653906, "results": "188", "hashOfConfig": "132"}, {"size": 281, "mtime": 1747109266546, "results": "189", "hashOfConfig": "132"}, {"size": 1430, "mtime": 1751272204657, "results": "190", "hashOfConfig": "132"}, {"size": 8100, "mtime": 1749201001867, "results": "191", "hashOfConfig": "132"}, {"size": 1510, "mtime": 1751625231028, "results": "192", "hashOfConfig": "132"}, {"size": 354, "mtime": 1749530596920, "results": "193", "hashOfConfig": "132"}, {"size": 1779, "mtime": 1747797160877, "results": "194", "hashOfConfig": "132"}, {"size": 1017, "mtime": 1747109266581, "results": "195", "hashOfConfig": "132"}, {"size": 2988, "mtime": 1747109266582, "results": "196", "hashOfConfig": "132"}, {"size": 795, "mtime": 1747109266583, "results": "197", "hashOfConfig": "132"}, {"size": 612, "mtime": 1747289688605, "results": "198", "hashOfConfig": "132"}, {"size": 15043, "mtime": 1752465854143, "results": "199", "hashOfConfig": "132"}, {"size": 4251, "mtime": 1751523077949, "results": "200", "hashOfConfig": "132"}, {"size": 49538, "mtime": 1752465855153, "results": "201", "hashOfConfig": "132"}, {"size": 6782, "mtime": 1752489573453, "results": "202", "hashOfConfig": "132"}, {"size": 1472, "mtime": 1747797160878, "results": "203", "hashOfConfig": "132"}, {"size": 9128, "mtime": 1752465855203, "results": "204", "hashOfConfig": "132"}, {"size": 1923, "mtime": 1747797160861, "results": "205", "hashOfConfig": "132"}, {"size": 258, "mtime": 1747797160876, "results": "206", "hashOfConfig": "132"}, {"size": 1933, "mtime": 1747797160878, "results": "207", "hashOfConfig": "132"}, {"size": 16693, "mtime": 1749486774218, "results": "208", "hashOfConfig": "132"}, {"size": 2307, "mtime": 1753182043457, "results": "209", "hashOfConfig": "132"}, {"size": 11914, "mtime": 1749486774211, "results": "210", "hashOfConfig": "132"}, {"size": 1489, "mtime": 1753182043461, "results": "211", "hashOfConfig": "132"}, {"size": 27319, "mtime": 1752465853271, "results": "212", "hashOfConfig": "132"}, {"size": 7414, "mtime": 1748768935822, "results": "213", "hashOfConfig": "132"}, {"size": 30405, "mtime": 1752465852869, "results": "214", "hashOfConfig": "132"}, {"size": 4054, "mtime": 1748363529403, "results": "215", "hashOfConfig": "132"}, {"size": 847, "mtime": 1748768935825, "results": "216", "hashOfConfig": "132"}, {"size": 236, "mtime": 1751276652281, "results": "217", "hashOfConfig": "132"}, {"size": 9836, "mtime": 1749201001627, "results": "218", "hashOfConfig": "132"}, {"size": 14847, "mtime": 1749201001637, "results": "219", "hashOfConfig": "132"}, {"size": 12466, "mtime": 1749201001662, "results": "220", "hashOfConfig": "132"}, {"size": 8747, "mtime": 1749201001679, "results": "221", "hashOfConfig": "132"}, {"size": 9707, "mtime": 1749201001681, "results": "222", "hashOfConfig": "132"}, {"size": 6260, "mtime": 1749201001698, "results": "223", "hashOfConfig": "132"}, {"size": 10604, "mtime": 1749201001711, "results": "224", "hashOfConfig": "132"}, {"size": 13037, "mtime": 1749486774214, "results": "225", "hashOfConfig": "132"}, {"size": 4849, "mtime": 1749201001776, "results": "226", "hashOfConfig": "132"}, {"size": 2628, "mtime": 1749201001787, "results": "227", "hashOfConfig": "132"}, {"size": 9446, "mtime": 1749201001792, "results": "228", "hashOfConfig": "132"}, {"size": 18789, "mtime": 1749486774215, "results": "229", "hashOfConfig": "132"}, {"size": 37891, "mtime": 1749486774217, "results": "230", "hashOfConfig": "132"}, {"size": 31713, "mtime": 1753156971365, "results": "231", "hashOfConfig": "132"}, {"size": 4268, "mtime": 1753156971405, "results": "232", "hashOfConfig": "132"}, {"size": 3207, "mtime": 1749486774424, "results": "233", "hashOfConfig": "132"}, {"size": 1365, "mtime": 1751276652291, "results": "234", "hashOfConfig": "132"}, {"size": 1187, "mtime": 1749486774234, "results": "235", "hashOfConfig": "132"}, {"size": 3013, "mtime": 1752465855220, "results": "236", "hashOfConfig": "132"}, {"size": 8011, "mtime": 1752465855888, "results": "237", "hashOfConfig": "132"}, {"size": 8834, "mtime": 1752465856640, "results": "238", "hashOfConfig": "132"}, {"size": 1675, "mtime": 1750649653907, "results": "239", "hashOfConfig": "132"}, {"size": 714, "mtime": 1753156971428, "results": "240", "hashOfConfig": "132"}, {"size": 2118, "mtime": 1750649653908, "results": "241", "hashOfConfig": "132"}, {"size": 554, "mtime": 1750649653852, "results": "242", "hashOfConfig": "132"}, {"size": 13617, "mtime": 1753182043460, "results": "243", "hashOfConfig": "132"}, {"size": 1291, "mtime": 1750649653906, "results": "244", "hashOfConfig": "132"}, {"size": 3822, "mtime": 1753182043458, "results": "245", "hashOfConfig": "132"}, {"size": 21456, "mtime": 1751625230898, "results": "246", "hashOfConfig": "132"}, {"size": 9775, "mtime": 1751625230867, "results": "247", "hashOfConfig": "132"}, {"size": 4673, "mtime": 1751625231663, "results": "248", "hashOfConfig": "132"}, {"size": 5474, "mtime": 1752465851656, "results": "249", "hashOfConfig": "132"}, {"size": 13037, "mtime": 1752489573348, "results": "250", "hashOfConfig": "132"}, {"size": 12800, "mtime": 1752489573359, "results": "251", "hashOfConfig": "132"}, {"size": 13012, "mtime": 1752489573392, "results": "252", "hashOfConfig": "132"}, {"size": 12623, "mtime": 1752489573414, "results": "253", "hashOfConfig": "132"}, {"size": 34882, "mtime": 1752465855130, "results": "254", "hashOfConfig": "132"}, {"size": 1574, "mtime": 1752465850247, "results": "255", "hashOfConfig": "132"}, {"size": 1868, "mtime": 1752465850416, "results": "256", "hashOfConfig": "132"}, {"size": 2012, "mtime": 1752465856751, "results": "257", "hashOfConfig": "132"}, {"size": 5280, "mtime": 1752917960042, "results": "258", "hashOfConfig": "132"}, {"size": 3364, "mtime": 1752465857138, "results": "259", "hashOfConfig": "132"}, {"size": 10929, "mtime": 1753201457357, "results": "260", "hashOfConfig": "132"}, {"size": 338, "mtime": 1753182055480, "results": "261", "hashOfConfig": "132"}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "k18kcd", {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\blog\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\blog\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-details\\[id]\\page.tsx", [], ["652"], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-thoughts\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\dashboard\\data-table.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\dashboard\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\reviews\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\testimonials\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-detail\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-detail\\result\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\quiz-termination-log\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\examSlice.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\layout.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\login\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\ReduxProvider.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\store.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\app-sidebar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\dataTable.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\nav-main.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\nav-user.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\site-header.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ConfirmationDialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ExamForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ExamTable.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\TestimonialTable.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\accordion.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\alert-dialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\avatar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\badge.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\button.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\card.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\chart.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\checkbox.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\dialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\dropdown-menu.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\form.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\input.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\label.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\select.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\separator.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sheet.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sidebar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\skeleton.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\sonner.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\table.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\tabs.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\toggle-group.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\toggle.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\tooltip.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\hooks\\use-mobile.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\axios.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\types.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\utils.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\examSchema.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\auth.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\blogApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classesApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classesThoughtApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\email.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examApplicationApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\questionApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\quizTerminationLog.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\reviewsApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-details\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\studentApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\question-bank\\page.tsx", ["653"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\questionBankApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\testimonialApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\rank-price\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\PriceRankTable.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\priceRankSchema.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizPriceRankApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-profile\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-applicant\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\StudentProfileModal.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizExamApplicantApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\ReferralFilters.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\referral-management\\[linkId]\\referred-users\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\calendar.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\not-found.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\chat\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddCertificateForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddEducationForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\AddExperienceForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\CertificateTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\EducationTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\ImagesTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\ProfileTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionClassForm.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionClassList.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\TuitionTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\components\\WorkTab.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-edit\\[id]\\page.tsx", [], ["654"], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\student-edit\\[id]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\AdminChat.tsx", ["655", "656"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\ui\\multi-select.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\lib\\validations\\classesEditSchema.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\chatApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-prefrence\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\uwhiz-terminated-students\\[examId]\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\LevelPrenceManager.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\SubjectPreferenceManager.tsx", ["657"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizLevelPrefrenceApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizQuizTerminationLogApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\uwhizSubjectPrefrenceApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\exam-monitoring\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\components\\StudentPhotoMonitoring.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\examMonitoringApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\uwhiz-result\\[examId]\\page.tsx", ["658"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\notifications\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\AdminNotificationBell.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\notificationService.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\classes-student\\[id]\\page.tsx", ["659", "660"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\detail\\[detailId]\\page.tsx", ["661"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\detail\\[detailId]\\sub-detail\\[subDetailId]\\page.tsx", ["662"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\category\\[id]\\page.tsx", [], ["663"], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\constants\\page.tsx", [], ["664"], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(examDashboard)\\mock-question-bank\\page.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\ConfirmDialog.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app-components\\pagination.tsx", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\classes-student.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\constantsApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\mock-examApi.ts", [], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\app\\(dashboard)\\activity-logs\\page.tsx", ["665"], [], "G:\\UEST\\uest_app\\uest-app\\admin\\src\\services\\activityLogApi.ts", [], [], {"ruleId": "666", "severity": 1, "message": "667", "line": 67, "column": 6, "nodeType": "668", "endLine": 67, "endColumn": 14, "suggestions": "669", "suppressions": "670"}, {"ruleId": "666", "severity": 1, "message": "671", "line": 254, "column": 6, "nodeType": "668", "endLine": 254, "endColumn": 13, "suggestions": "672"}, {"ruleId": "666", "severity": 1, "message": "673", "line": 231, "column": 6, "nodeType": "668", "endLine": 231, "endColumn": 15, "suggestions": "674", "suppressions": "675"}, {"ruleId": "666", "severity": 1, "message": "676", "line": 107, "column": 8, "nodeType": "668", "endLine": 107, "endColumn": 56, "suggestions": "677"}, {"ruleId": "666", "severity": 1, "message": "678", "line": 117, "column": 8, "nodeType": "668", "endLine": 117, "endColumn": 57, "suggestions": "679"}, {"ruleId": "666", "severity": 1, "message": "680", "line": 127, "column": 6, "nodeType": "668", "endLine": 127, "endColumn": 8, "suggestions": "681"}, {"ruleId": "666", "severity": 1, "message": "682", "line": 62, "column": 6, "nodeType": "668", "endLine": 62, "endColumn": 20, "suggestions": "683"}, {"ruleId": "666", "severity": 1, "message": "684", "line": 107, "column": 6, "nodeType": "668", "endLine": 107, "endColumn": 15, "suggestions": "685"}, {"ruleId": "666", "severity": 1, "message": "686", "line": 111, "column": 6, "nodeType": "668", "endLine": 111, "endColumn": 33, "suggestions": "687"}, {"ruleId": "666", "severity": 1, "message": "688", "line": 228, "column": 6, "nodeType": "668", "endLine": 228, "endColumn": 19, "suggestions": "689"}, {"ruleId": "666", "severity": 1, "message": "690", "line": 225, "column": 6, "nodeType": "668", "endLine": 225, "endColumn": 22, "suggestions": "691"}, {"ruleId": "666", "severity": 1, "message": "692", "line": 234, "column": 6, "nodeType": "668", "endLine": 234, "endColumn": 21, "suggestions": "693", "suppressions": "694"}, {"ruleId": "666", "severity": 1, "message": "695", "line": 225, "column": 6, "nodeType": "668", "endLine": 225, "endColumn": 8, "suggestions": "696", "suppressions": "697"}, {"ruleId": "666", "severity": 1, "message": "698", "line": 76, "column": 6, "nodeType": "668", "endLine": 76, "endColumn": 8, "suggestions": "699"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchTeacher'. Either include it or remove the dependency array.", "ArrayExpression", ["700"], ["701"], "React Hook useEffect has missing dependencies: 'fetchConstants' and 'fetchQuestions'. Either include them or remove the dependency array.", ["702"], "React Hook useEffect has a missing dependency: 'fetchClassData'. Either include it or remove the dependency array.", ["703"], ["704"], "React Hook useEffect has a missing dependency: 'classes.length'. Either include it or remove the dependency array.", ["705"], "React Hook useEffect has a missing dependency: 'students.length'. Either include it or remove the dependency array.", ["706"], "React Hook useCallback has a missing dependency: 'examId'. Either include it or remove the dependency array.", ["707"], "React Hook useEffect has a missing dependency: 'fetchRankings'. Either include it or remove the dependency array.", ["708"], "React Hook useEffect has missing dependencies: 'currentPage', 'getStudents', 'getYears', 'searchTerm', and 'selectedYear'. Either include them or remove the dependency array.", ["709"], "React Hook useEffect has missing dependencies: 'getStudents' and 'searchTerm'. Either include them or remove the dependency array.", ["710"], "React Hook useEffect has a missing dependency: 'fetchSubDetails'. Either include it or remove the dependency array.", ["711"], "React Hook useEffect has a missing dependency: 'fetchValues'. Either include it or remove the dependency array.", ["712"], "React Hook useEffect has a missing dependency: 'fetchDetails'. Either include it or remove the dependency array.", ["713"], ["714"], "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array.", ["715"], ["716"], "React Hook useEffect has a missing dependency: 'fetchActivityLogs'. Either include it or remove the dependency array.", ["717"], {"desc": "718", "fix": "719"}, {"kind": "720", "justification": "721"}, {"desc": "722", "fix": "723"}, {"desc": "724", "fix": "725"}, {"kind": "720", "justification": "721"}, {"desc": "726", "fix": "727"}, {"desc": "728", "fix": "729"}, {"desc": "730", "fix": "731"}, {"desc": "732", "fix": "733"}, {"desc": "734", "fix": "735"}, {"desc": "736", "fix": "737"}, {"desc": "738", "fix": "739"}, {"desc": "740", "fix": "741"}, {"desc": "742", "fix": "743"}, {"kind": "720", "justification": "721"}, {"desc": "744", "fix": "745"}, {"kind": "720", "justification": "721"}, {"desc": "746", "fix": "747"}, "Update the dependencies array to be: [fetchTeacher, userId]", {"range": "748", "text": "749"}, "directive", "", "Update the dependencies array to be: [fetchConstants, fetchQuestions, limit]", {"range": "750", "text": "751"}, "Update the dependencies array to be: [classId, fetchClassData]", {"range": "752", "text": "753"}, "Update the dependencies array to be: [classSearchQuery, classes.length, isAuthenticated, loadClasses]", {"range": "754", "text": "755"}, "Update the dependencies array to be: [studentSearchQuery, selectedClass, loadStudents, students.length]", {"range": "756", "text": "757"}, "Update the dependencies array to be: [examId]", {"range": "758", "text": "759"}, "Update the dependencies array to be: [examId, fetchRankings, page]", {"range": "760", "text": "761"}, "Update the dependencies array to be: [classId, currentPage, getStudents, getYears, searchTerm, selectedYear]", {"range": "762", "text": "763"}, "Update the dependencies array to be: [currentPage, getStudents, searchTerm, selectedYear]", {"range": "764", "text": "765"}, "Update the dependencies array to be: [fetchDetail, fetchSubDetails]", {"range": "766", "text": "767"}, "Update the dependencies array to be: [fetchSubDetail, fetchValues]", {"range": "768", "text": "769"}, "Update the dependencies array to be: [fetchCategory, fetchDetails]", {"range": "770", "text": "771"}, "Update the dependencies array to be: [fetchCategories]", {"range": "772", "text": "773"}, "Update the dependencies array to be: [fetchActivityLogs]", {"range": "774", "text": "775"}, [2175, 2183], "[fetchTeacher, userId]", [9004, 9011], "[fetchConstants, fetchQuestions, limit]", [7433, 7442], "[classId, fetchClassData]", [3971, 4019], "[classSearch<PERSON><PERSON>y, classes.length, isAuthenticated, loadClasses]", [4337, 4386], "[studentSearch<PERSON><PERSON>y, selectedClass, loadStudents, students.length]", [3343, 3345], "[examId]", [1799, 1813], "[examId, fetchRankings, page]", [3073, 3082], "[classId, currentPage, getStudents, getYears, searchTerm, selectedYear]", [3171, 3198], "[currentPage, getStudents, searchTerm, selectedYear]", [7233, 7246], "[fetchDetail, fetchSubDetails]", [6733, 6749], "[fetchSubDetail, fetchValues]", [7331, 7346], "[fetch<PERSON>ate<PERSON>y, fetchDetails]", [7161, 7163], "[fetchCategories]", [2816, 2818], "[fetchActivityLogs]"]