{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app/%28dashboard%29/store/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Plus, Edit, Trash2, Package, Search, Filter, Eye } from 'lucide-react';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { DataTable } from '@/app-components/dataTable';\r\nimport { ColumnDef } from '@tanstack/react-table';\r\nimport { toast } from 'sonner';\r\nimport Image from 'next/image';\r\n\r\n// Types\r\ninterface StoreItem {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  price: number;\r\n  coinPrice: number;\r\n  quantity: number;\r\n  category: string;\r\n  image: string;\r\n  status: 'active' | 'inactive';\r\n  createdAt: string;\r\n}\r\n\r\ninterface AddItemFormData {\r\n  name: string;\r\n  description: string;\r\n  price: number;\r\n  coinPrice: number;\r\n  quantity: number;\r\n  category: string;\r\n  image: File | null;\r\n}\r\n\r\nconst categories = [\r\n  'Stationery',\r\n  'Electronics',\r\n  'Apparel',\r\n  'Accessories',\r\n  'Digital',\r\n  'Books',\r\n  'Sports',\r\n  'Other'\r\n];\r\n\r\n// Mock data for demonstration\r\nconst mockStoreItems: StoreItem[] = [\r\n  {\r\n    id: '1',\r\n    name: 'UEST Premium Notebook Set',\r\n    description: 'High-quality notebooks perfect for students. Set of 5 notebooks with different subjects.',\r\n    price: 299,\r\n    coinPrice: 150,\r\n    quantity: 50,\r\n    category: 'Stationery',\r\n    image: '/logo.png',\r\n    status: 'active',\r\n    createdAt: '2024-01-15T10:30:00Z'\r\n  },\r\n  {\r\n    id: '2',\r\n    name: 'Scientific Calculator',\r\n    description: 'Advanced scientific calculator for mathematics and engineering students.',\r\n    price: 899,\r\n    coinPrice: 450,\r\n    quantity: 25,\r\n    category: 'Electronics',\r\n    image: '/uwhizExam.png',\r\n    status: 'active',\r\n    createdAt: '2024-01-14T14:20:00Z'\r\n  },\r\n  {\r\n    id: '3',\r\n    name: 'UEST Branded T-Shirt',\r\n    description: 'Comfortable cotton t-shirt with UEST logo. Available in multiple sizes.',\r\n    price: 499,\r\n    coinPrice: 250,\r\n    quantity: 0,\r\n    category: 'Apparel',\r\n    image: '/exam-logo.jpg',\r\n    status: 'inactive',\r\n    createdAt: '2024-01-13T09:15:00Z'\r\n  }\r\n];\r\n\r\nconst StorePage = () => {\r\n  const [items, setItems] = useState<StoreItem[]>(mockStoreItems);\r\n  const [filteredItems, setFilteredItems] = useState<StoreItem[]>(mockStoreItems);\r\n  const [isAddModalOpen, setIsAddModalOpen] = useState(false);\r\n  const [isEditModalOpen, setIsEditModalOpen] = useState(false);\r\n  const [selectedItem, setSelectedItem] = useState<StoreItem | null>(null);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\r\n  const [formData, setFormData] = useState<AddItemFormData>({\r\n    name: '',\r\n    description: '',\r\n    price: 0,\r\n    coinPrice: 0,\r\n    quantity: 0,\r\n    category: '',\r\n    image: null\r\n  });\r\n\r\n  // Filter items based on search and category\r\n  useEffect(() => {\r\n    let filtered = items;\r\n\r\n    if (searchQuery) {\r\n      filtered = filtered.filter(item =>\r\n        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        item.description.toLowerCase().includes(searchQuery.toLowerCase())\r\n      );\r\n    }\r\n\r\n    if (selectedCategory !== 'all') {\r\n      filtered = filtered.filter(item => item.category === selectedCategory);\r\n    }\r\n\r\n    setFilteredItems(filtered);\r\n  }, [items, searchQuery, selectedCategory]);\r\n\r\n  // Handle form submission\r\n  const handleAddItem = () => {\r\n    if (!formData.name || !formData.description || !formData.category) {\r\n      toast.error('Please fill in all required fields');\r\n      return;\r\n    }\r\n\r\n    const newItem: StoreItem = {\r\n      id: Date.now().toString(),\r\n      name: formData.name,\r\n      description: formData.description,\r\n      price: formData.price,\r\n      coinPrice: formData.coinPrice,\r\n      quantity: formData.quantity,\r\n      category: formData.category,\r\n      image: formData.image ? URL.createObjectURL(formData.image) : '/logo.png',\r\n      status: formData.quantity > 0 ? 'active' : 'inactive',\r\n      createdAt: new Date().toISOString()\r\n    };\r\n\r\n    setItems([newItem, ...items]);\r\n    setIsAddModalOpen(false);\r\n    resetForm();\r\n    toast.success('Item added successfully!');\r\n  };\r\n\r\n  // Handle edit item\r\n  const handleEditItem = () => {\r\n    if (!selectedItem || !formData.name || !formData.description || !formData.category) {\r\n      toast.error('Please fill in all required fields');\r\n      return;\r\n    }\r\n\r\n    const updatedItems = items.map(item =>\r\n      item.id === selectedItem.id\r\n        ? {\r\n            ...item,\r\n            name: formData.name,\r\n            description: formData.description,\r\n            price: formData.price,\r\n            coinPrice: formData.coinPrice,\r\n            quantity: formData.quantity,\r\n            category: formData.category,\r\n            image: formData.image ? URL.createObjectURL(formData.image) : item.image,\r\n            status: formData.quantity > 0 ? 'active' : 'inactive'\r\n          }\r\n        : item\r\n    );\r\n\r\n    setItems(updatedItems);\r\n    setIsEditModalOpen(false);\r\n    setSelectedItem(null);\r\n    resetForm();\r\n    toast.success('Item updated successfully!');\r\n  };\r\n\r\n  // Handle delete item\r\n  const handleDeleteItem = (id: string) => {\r\n    setItems(items.filter(item => item.id !== id));\r\n    toast.success('Item deleted successfully!');\r\n  };\r\n\r\n  // Reset form\r\n  const resetForm = () => {\r\n    setFormData({\r\n      name: '',\r\n      description: '',\r\n      price: 0,\r\n      coinPrice: 0,\r\n      quantity: 0,\r\n      category: '',\r\n      image: null\r\n    });\r\n  };\r\n\r\n  // Open edit modal\r\n  const openEditModal = (item: StoreItem) => {\r\n    setSelectedItem(item);\r\n    setFormData({\r\n      name: item.name,\r\n      description: item.description,\r\n      price: item.price,\r\n      coinPrice: item.coinPrice,\r\n      quantity: item.quantity,\r\n      category: item.category,\r\n      image: null\r\n    });\r\n    setIsEditModalOpen(true);\r\n  };"], "names": [], "mappings": ";AAEA;AA0BA;;AA5BA;;;AAuDA,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,8BAA8B;AAC9B,MAAM,iBAA8B;IAClC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,WAAW;QACX,UAAU;QACV,UAAU;QACV,OAAO;QACP,QAAQ;QACR,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,WAAW;QACX,UAAU;QACV,UAAU;QACV,OAAO;QACP,QAAQ;QACR,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,WAAW;QACX,UAAU;QACV,UAAU;QACV,OAAO;QACP,QAAQ;QACR,WAAW;IACb;CACD;AAED,MAAM,YAAY;;IAChB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAChD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAChE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD,MAAM;QACN,aAAa;QACb,OAAO;QACP,WAAW;QACX,UAAU;QACV,UAAU;QACV,OAAO;IACT;IAEA,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,WAAW;YAEf,IAAI,aAAa;gBACf,WAAW,SAAS,MAAM;2CAAC,CAAA,OACzB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACxD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;;YAEnE;YAEA,IAAI,qBAAqB,OAAO;gBAC9B,WAAW,SAAS,MAAM;2CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;;YACvD;YAEA,iBAAiB;QACnB;8BAAG;QAAC;QAAO;QAAa;KAAiB;IAEzC,yBAAyB;IACzB,MAAM,gBAAgB;QACpB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,QAAQ,EAAE;YACjE,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,UAAqB;YACzB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM,SAAS,IAAI;YACnB,aAAa,SAAS,WAAW;YACjC,OAAO,SAAS,KAAK;YACrB,WAAW,SAAS,SAAS;YAC7B,UAAU,SAAS,QAAQ;YAC3B,UAAU,SAAS,QAAQ;YAC3B,OAAO,SAAS,KAAK,GAAG,IAAI,eAAe,CAAC,SAAS,KAAK,IAAI;YAC9D,QAAQ,SAAS,QAAQ,GAAG,IAAI,WAAW;YAC3C,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,SAAS;YAAC;eAAY;SAAM;QAC5B,kBAAkB;QAClB;QACA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,IAAI,CAAC,gBAAgB,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,QAAQ,EAAE;YAClF,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,eAAe,MAAM,GAAG,CAAC,CAAA,OAC7B,KAAK,EAAE,KAAK,aAAa,EAAE,GACvB;gBACE,GAAG,IAAI;gBACP,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW;gBACjC,OAAO,SAAS,KAAK;gBACrB,WAAW,SAAS,SAAS;gBAC7B,UAAU,SAAS,QAAQ;gBAC3B,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,KAAK,GAAG,IAAI,eAAe,CAAC,SAAS,KAAK,IAAI,KAAK,KAAK;gBACxE,QAAQ,SAAS,QAAQ,GAAG,IAAI,WAAW;YAC7C,IACA;QAGN,SAAS;QACT,mBAAmB;QACnB,gBAAgB;QAChB;QACA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC1C,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,aAAa;IACb,MAAM,YAAY;QAChB,YAAY;YACV,MAAM;YACN,aAAa;YACb,OAAO;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,OAAO;QACT;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,gBAAgB;QAChB,YAAY;YACV,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW;YAC7B,OAAO,KAAK,KAAK;YACjB,WAAW,KAAK,SAAS;YACzB,UAAU,KAAK,QAAQ;YACvB,UAAU,KAAK,QAAQ;YACvB,OAAO;QACT;QACA,mBAAmB;IACrB;AAAC;GA5HG;KAAA", "debugId": null}}]}