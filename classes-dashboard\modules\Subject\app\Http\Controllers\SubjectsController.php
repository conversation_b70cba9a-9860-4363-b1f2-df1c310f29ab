<?php

namespace Subject\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Subject\Http\Requests\CreateSubjectRequest;
use Classroom\Repositories\ClassroomRepository;
use Subject\Repositories\SubjectRepository;

class SubjectsController extends Controller
{
    protected $subjectRepository;
    protected $classroomRepository;
    public function __construct(SubjectRepository $subjectRepository, ClassroomRepository $classroomRepository)
    {
        $this->middleware('permission:read subject', ['only' => ['index']]);
        $this->middleware('permission:create subject', ['only' => ['create', 'store']]);
        $this->middleware('permission:update subject', ['only' => ['edit', 'update']]);
        $this->middleware('permission:delete subject', ['only' => ['destroy']]); 
        $this->middleware('permission:export subject data', ['only' => ['exportSubjects']]);       
        $this->middleware('permission:transfer subject data', ['only' => ['transferSubjects']]);       
        $this->subjectRepository = $subjectRepository;
        $this->classroomRepository = $classroomRepository;
    }

    public function index(Request $request)
    {
        $department = departmentForStudentPortal();
        if (request()->ajax()) {
            $list = $this->subjectRepository->getAll($request);
            return $this->subjectRepository->getDatatable($list);
        }
        return view('Subject::index', compact('department'));
    }

    public function create(Request $request)
    {
        $classroom = $this->classroomRepository->getAll($request)->get();
        return view('Subject::create',compact('classroom'));
    }

    public function store(CreateSubjectRequest $request)
    {

        $this->subjectRepository->storeSubject($request);
        return response()->json(['success' => 'Subject Created Successfully!!']);
    }

    public function edit(Request $request, $id)
    {
        $data = $this->subjectRepository->getSubjectById($id);
        $classroom = $this->classroomRepository->getAll($request)->get();
        return view('Subject::edit', compact('data','classroom'));
    }

    public function update(CreateSubjectRequest $request,$id)
    {
        $this->subjectRepository->updateSubject($request,$id);
        return response()->json(['success' => 'Subject Updated successfully!!']);
    }

    public function destroy($id)
    {
        $subject = $this->subjectRepository->getSubjectById($id);
        $subject->delete();
        return response()->json(['success' => 'Subject deleted successfully!!']);//
    }

    public function getsubjectlist(Request $request)
    {
      $subject = $this->subjectRepository->getSubjectListByClassroom($request);
      return response()->json(array('subject' => $subject));
    }

    public function exportSubjects(Request $request) 
    {    
        $subjects = $this->subjectRepository->getAll($request)->get();
        return commonExport($subjects, 'Subject::export', 'subjects');
    }
}
