{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from 'react';\r\n\r\nconst MOBILE_BREAKPOINT = 768;\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined);\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\r\n    };\r\n    mql.addEventListener('change', onChange);\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\r\n    return () => mql.removeEventListener('change', onChange);\r\n  }, []);\r\n\r\n  return !!isMobile;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;YACvE,MAAM;kDAAW;oBACf,YAAY,OAAO,UAAU,GAAG;gBAClC;;YACA,IAAI,gBAAgB,CAAC,UAAU;YAC/B,YAAY,OAAO,UAAU,GAAG;YAChC;yCAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;;QACjD;gCAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX;GAdgB", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const truncateThought = (text: string, wordLimit: number = 5): string => {\r\n  const words = text.trim().split(/\\s+/);\r\n  if (words.length <= wordLimit) return text;\r\n  return words.slice(0, wordLimit).join(' ') + '...';\r\n};"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,kBAAkB,CAAC,MAAc,YAAoB,CAAC;IACjE,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,IAAI,WAAW,OAAO;IACtC,OAAO,MAAM,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,OAAO;AAC/C", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<'input'>) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        'file:text-foreground mt-2 placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n        'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',\r\n        'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/separator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SeparatorPrimitive from '@radix-ui/react-separator';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = 'horizontal',\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        'bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Separator };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/sheet.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SheetPrimitive from '@radix-ui/react-dialog';\r\nimport { XIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />;\r\n}\r\n\r\nfunction SheetTrigger({ ...props }: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />;\r\n}\r\n\r\nfunction SheetClose({ ...props }: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />;\r\n}\r\n\r\nfunction SheetPortal({ ...props }: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />;\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = 'right',\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: 'top' | 'right' | 'bottom' | 'left';\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500',\r\n          side === 'right' &&\r\n            'data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm',\r\n          side === 'left' &&\r\n            'data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm',\r\n          side === 'top' &&\r\n            'data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b',\r\n          side === 'bottom' &&\r\n            'data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  );\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn('flex flex-col gap-1.5 p-4', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn('mt-auto flex flex-col gap-2 p-4', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SheetTitle({ className, ...props }: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn('text-foreground font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EAAE,GAAG,OAA4D;IACrF,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAFS;AAIT,SAAS,WAAW,EAAE,GAAG,OAA0D;IACjF,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAFS;AAIT,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAFS;AAIT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAA0D;IAC5F,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils';\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn('bg-accent animate-pulse rounded-md', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KARS", "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/tooltip.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as TooltipPrimitive from '@radix-ui/react-tooltip';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction Tooltip({ ...props }: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  );\r\n}\r\n\r\nfunction TooltipTrigger({ ...props }: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />;\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  );\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,6LAAC,sKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,EAAE,GAAG,OAA2D;IAC/E,qBACE,6LAAC;kBACC,cAAA,6LAAC,sKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MANS;AAQT,SAAS,eAAe,EAAE,GAAG,OAA8D;IACzF,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAFS;AAIT,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,sKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/sidebar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { VariantProps, cva } from 'class-variance-authority';\r\nimport { PanelLeftIcon } from 'lucide-react';\r\n\r\nimport { useIsMobile } from '@/hooks/use-mobile';\r\nimport { cn } from '@/lib/utils';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from '@/components/ui/sheet';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\r\n\r\nconst SIDEBAR_COOKIE_NAME = 'sidebar_state';\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\r\nconst SIDEBAR_WIDTH = '16rem';\r\nconst SIDEBAR_WIDTH_MOBILE = '18rem';\r\nconst SIDEBAR_WIDTH_ICON = '3rem';\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = 'b';\r\n\r\ntype SidebarContextProps = {\r\n  state: 'expanded' | 'collapsed';\r\n  open: boolean;\r\n  setOpen: (open: boolean) => void;\r\n  openMobile: boolean;\r\n  setOpenMobile: (open: boolean) => void;\r\n  isMobile: boolean;\r\n  toggleSidebar: () => void;\r\n};\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext);\r\n  if (!context) {\r\n    throw new Error('useSidebar must be used within a SidebarProvider.');\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<'div'> & {\r\n  defaultOpen?: boolean;\r\n  open?: boolean;\r\n  onOpenChange?: (open: boolean) => void;\r\n}) {\r\n  const isMobile = useIsMobile();\r\n  const [openMobile, setOpenMobile] = React.useState(false);\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen);\r\n  const open = openProp ?? _open;\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === 'function' ? value(open) : value;\r\n      if (setOpenProp) {\r\n        setOpenProp(openState);\r\n      } else {\r\n        _setOpen(openState);\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\r\n    },\r\n    [setOpenProp, open]\r\n  );\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);\r\n  }, [isMobile, setOpen, setOpenMobile]);\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (event.key === SIDEBAR_KEYBOARD_SHORTCUT && (event.metaKey || event.ctrlKey)) {\r\n        event.preventDefault();\r\n        toggleSidebar();\r\n      }\r\n    };\r\n\r\n    window.addEventListener('keydown', handleKeyDown);\r\n    return () => window.removeEventListener('keydown', handleKeyDown);\r\n  }, [toggleSidebar]);\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? 'expanded' : 'collapsed';\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  );\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              '--sidebar-width': SIDEBAR_WIDTH,\r\n              '--sidebar-width-icon': SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            'group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full',\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  );\r\n}\r\n\r\nfunction Sidebar({\r\n  side = 'left',\r\n  variant = 'sidebar',\r\n  collapsible = 'offcanvas',\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<'div'> & {\r\n  side?: 'left' | 'right';\r\n  variant?: 'sidebar' | 'floating' | 'inset';\r\n  collapsible?: 'offcanvas' | 'icon' | 'none';\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\r\n\r\n  if (collapsible === 'none') {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          'bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              '--sidebar-width': SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === 'collapsed' ? collapsible : ''}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          'relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear',\r\n          'group-data-[collapsible=offcanvas]:w-0',\r\n          'group-data-[side=right]:rotate-180',\r\n          variant === 'floating' || variant === 'inset'\r\n            ? 'group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]'\r\n            : 'group-data-[collapsible=icon]:w-(--sidebar-width-icon)'\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          'fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex',\r\n          side === 'left'\r\n            ? 'left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]'\r\n            : 'right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]',\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === 'floating' || variant === 'inset'\r\n            ? 'p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]'\r\n            : 'group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarTrigger({ className, onClick, ...props }: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn('size-7', className)}\r\n      onClick={(event) => {\r\n        onClick?.(event);\r\n        toggleSidebar();\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  );\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<'button'>) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        'hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex',\r\n        'in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize',\r\n        '[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize',\r\n        'hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full',\r\n        '[[data-side=left][data-collapsible=offcanvas]_&]:-right-2',\r\n        '[[data-side=right][data-collapsible=offcanvas]_&]:-left-2',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<'main'>) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        'bg-background relative flex w-full flex-1 flex-col',\r\n        'md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInput({ className, ...props }: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn('bg-background h-8 w-full shadow-none', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn('flex flex-col gap-2 p-2', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn('flex flex-col gap-2 p-2', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarSeparator({ className, ...props }: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn('bg-sidebar-border mx-2 w-auto', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        'flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn('relative flex w-full min-w-0 flex-col p-2', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'div'> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : 'div';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        'text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0',\r\n        'group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        'text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0',\r\n        // Increases the hit area of the button on mobile.\r\n        'after:absolute after:-inset-2 md:after:hidden',\r\n        'group-data-[collapsible=icon]:hidden',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn('w-full text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<'ul'>) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn('flex w-full min-w-0 flex-col gap-1', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<'li'>) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn('group/menu-item relative', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  'peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground',\r\n        outline:\r\n          'bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]',\r\n      },\r\n      size: {\r\n        default: 'h-8 text-sm',\r\n        sm: 'h-7 text-xs',\r\n        lg: 'h-12 text-sm group-data-[collapsible=icon]:p-0!',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = 'default',\r\n  size = 'default',\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'button'> & {\r\n  asChild?: boolean;\r\n  isActive?: boolean;\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>;\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : 'button';\r\n  const { isMobile, state } = useSidebar();\r\n\r\n  const button = (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n\r\n  if (!tooltip) {\r\n    return button;\r\n  }\r\n\r\n  if (typeof tooltip === 'string') {\r\n    tooltip = {\r\n      children: tooltip,\r\n    };\r\n  }\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={state !== 'collapsed' || isMobile}\r\n        {...tooltip}\r\n      />\r\n    </Tooltip>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> & {\r\n  asChild?: boolean;\r\n  showOnHover?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        'text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0',\r\n        // Increases the hit area of the button on mobile.\r\n        'after:absolute after:-inset-2 md:after:hidden',\r\n        'peer-data-[size=sm]/menu-button:top-1',\r\n        'peer-data-[size=default]/menu-button:top-1.5',\r\n        'peer-data-[size=lg]/menu-button:top-2.5',\r\n        'group-data-[collapsible=icon]:hidden',\r\n        showOnHover &&\r\n          'peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuBadge({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        'text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none',\r\n        'peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground',\r\n        'peer-data-[size=sm]/menu-button:top-1',\r\n        'peer-data-[size=default]/menu-button:top-1.5',\r\n        'peer-data-[size=lg]/menu-button:top-2.5',\r\n        'group-data-[collapsible=icon]:hidden',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<'div'> & {\r\n  showIcon?: boolean;\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`;\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn('flex h-8 items-center gap-2 rounded-md px-2', className)}\r\n      {...props}\r\n    >\r\n      {showIcon && <Skeleton className=\"size-4 rounded-md\" data-sidebar=\"menu-skeleton-icon\" />}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            '--skeleton-width': width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<'ul'>) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        'border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5',\r\n        'group-data-[collapsible=icon]:hidden',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubItem({ className, ...props }: React.ComponentProps<'li'>) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn('group/menu-sub-item relative', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = 'md',\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'a'> & {\r\n  asChild?: boolean;\r\n  size?: 'sm' | 'md';\r\n  isActive?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : 'a';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        'text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0',\r\n        'data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground',\r\n        size === 'sm' && 'text-xs',\r\n        size === 'md' && 'text-sm',\r\n        'group-data-[collapsible=icon]:hidden',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;;;AApBA;;;;;;;;;;;;;AAsBA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GAPS;AAST,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;;IACC,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;gDAC9B,CAAC;YACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;YAC9D,IAAI,aAAa;gBACf,YAAY;YACd,OAAO;gBACL,SAAS;YACX;YAEA,kDAAkD;YAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;QACpG;+CACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;sDAAE;YACtC,OAAO,WAAW;8DAAc,CAAC,OAAS,CAAC;+DAAQ;8DAAQ,CAAC,OAAS,CAAC;;QACxE;qDAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;qCAAE;YACd,MAAM;2DAAgB,CAAC;oBACrB,IAAI,MAAM,GAAG,KAAK,6BAA6B,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAAG;wBAC/E,MAAM,cAAc;wBACpB;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;iDAC/B,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;gDACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,6LAAC,sIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,6LAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;IA7FS;;QAaU,gIAAA,CAAA,cAAW;;;KAbrB;AA+FT,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,6LAAC,oIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,6LAAC,oIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,oIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,6LAAC,oIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,6LAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,6LAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;IApGS;;QAYgD;;;MAZhD;AAsGT,SAAS,eAAe,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAA4C;;IAC3F,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,6LAAC,uNAAA,CAAA,gBAAa;;;;;0BACd,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;IApBS;;QACmB;;;MADnB;AAsBT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;IAvBS;;QACmB;;;MADnB;AAyBT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAA2C;IAC/E,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,iBAAiB,EAAE,SAAS,EAAE,GAAG,OAA+C;IACvF,qBACE,6LAAC,wIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;OAnBS;AAqBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OArBS;AAuBT,SAAS,oBAAoB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC/E,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,MAAM,4BAA4B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;;IAChD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC,sIAAA,CAAA,UAAO;;0BACN,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,6LAAC,sIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;IAhDS;;QAcqB;;;OAdrB;AAkDT,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;OA9BS;AAgCT,SAAS,iBAAiB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC5E,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OAjBS;AAmBT,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;8CAAE;YAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;QAClD;6CAAG,EAAE;IAEL,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BAAY,6LAAC,uIAAA,CAAA,WAAQ;gBAAC,WAAU;gBAAoB,gBAAa;;;;;;0BAClE,6LAAC,uIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;IA/BS;OAAA;AAiCT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OAbS;AAeT,SAAS,mBAAmB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC7E,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OA9BS", "debugId": null}}, {"offset": {"line": 1276, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app-components/nav-main.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { type LucideIcon } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport { useState } from 'react';\r\nimport { ChevronDown, ChevronRight } from 'lucide-react';\r\nimport {\r\n  SidebarGroup,\r\n  SidebarGroupContent,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n} from '@/components/ui/sidebar';\r\n\r\ninterface NavItem {\r\n  title: string;\r\n  url?: string;\r\n  icon?: LucideIcon;\r\n  children?: NavItem[];\r\n}\r\n\r\nexport function NavMain({\r\n  items,\r\n}: {\r\n  items: NavItem[];\r\n}) {\r\n  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({});\r\n\r\n  const toggleExpand = (title: string) => {\r\n    setExpandedItems(prev => ({\r\n      ...prev,\r\n      [title]: !prev[title]\r\n    }));\r\n  };\r\n\r\n  return (\r\n    <SidebarGroup>\r\n      <SidebarGroupContent className=\"flex flex-col gap-2\">\r\n        <SidebarMenu>\r\n          {items.map((item) => {\r\n            const hasChildren = item.children && item.children.length > 0;\r\n            const isExpanded = expandedItems[item.title];\r\n\r\n            return (\r\n              <SidebarMenuItem key={item.title}>\r\n                {hasChildren ? (\r\n                  <div className=\"w-full\">\r\n                    <div\r\n                      onClick={() => toggleExpand(item.title)}\r\n                      className=\"cursor-pointer\"\r\n                    >\r\n                      <SidebarMenuButton\r\n                        tooltip={item.title}\r\n                        className={`${isExpanded ? 'bg-gray-100' : ''} hover:bg-gray-100 transition-colors`}\r\n                      >\r\n                        {item.icon && <item.icon />}\r\n                        <span>{item.title}</span>\r\n                        <span className=\"ml-auto\">\r\n                          {isExpanded ?\r\n                            <ChevronDown className=\"h-4 w-4 text-gray-500\" /> :\r\n                            <ChevronRight className=\"h-4 w-4 text-gray-500\" />\r\n                          }\r\n                        </span>\r\n                      </SidebarMenuButton>\r\n                    </div>\r\n\r\n                    {isExpanded && (\r\n                      <div className=\"ml-6 mt-2 border-l-2 border-gray-200 pl-2 space-y-1 animate-in fade-in-50 duration-200\">\r\n                        {item.children?.map((child) => (\r\n                          <Link href={child.url || '#'} key={child.title}>\r\n                            <SidebarMenuButton tooltip={child.title} className=\"py-1.5 hover:bg-gray-100 rounded-md transition-colors\">\r\n                              {child.icon && <child.icon className=\"h-4 w-4 mr-2\" />}\r\n                              <span className=\"text-sm\">{child.title}</span>\r\n                            </SidebarMenuButton>\r\n                          </Link>\r\n                        ))}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                ) : (\r\n                  <Link href={item.url || '#'}>\r\n                    <SidebarMenuButton tooltip={item.title}>\r\n                      {item.icon && <item.icon />}\r\n                      <span>{item.title}</span>\r\n                    </SidebarMenuButton>\r\n                  </Link>\r\n                )}\r\n              </SidebarMenuItem>\r\n            );\r\n          })}\r\n        </SidebarMenu>\r\n      </SidebarGroupContent>\r\n    </SidebarGroup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AACA;;;AANA;;;;;AAqBO,SAAS,QAAQ,EACtB,KAAK,EAGN;;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAE7E,MAAM,eAAe,CAAC;QACpB,iBAAiB,CAAA,OAAQ,CAAC;gBACxB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM;YACvB,CAAC;IACH;IAEA,qBACE,6LAAC,sIAAA,CAAA,eAAY;kBACX,cAAA,6LAAC,sIAAA,CAAA,sBAAmB;YAAC,WAAU;sBAC7B,cAAA,6LAAC,sIAAA,CAAA,cAAW;0BACT,MAAM,GAAG,CAAC,CAAC;oBACV,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;oBAC5D,MAAM,aAAa,aAAa,CAAC,KAAK,KAAK,CAAC;oBAE5C,qBACE,6LAAC,sIAAA,CAAA,kBAAe;kCACb,4BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,aAAa,KAAK,KAAK;oCACtC,WAAU;8CAEV,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;wCAChB,SAAS,KAAK,KAAK;wCACnB,WAAW,GAAG,aAAa,gBAAgB,GAAG,oCAAoC,CAAC;;4CAElF,KAAK,IAAI,kBAAI,6LAAC,KAAK,IAAI;;;;;0DACxB,6LAAC;0DAAM,KAAK,KAAK;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DACb,2BACC,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEACvB,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gCAM/B,4BACC,6LAAC;oCAAI,WAAU;8CACZ,KAAK,QAAQ,EAAE,IAAI,CAAC,sBACnB,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM,MAAM,GAAG,IAAI;sDACvB,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;gDAAC,SAAS,MAAM,KAAK;gDAAE,WAAU;;oDAChD,MAAM,IAAI,kBAAI,6LAAC,MAAM,IAAI;wDAAC,WAAU;;;;;;kEACrC,6LAAC;wDAAK,WAAU;kEAAW,MAAM,KAAK;;;;;;;;;;;;2CAHP,MAAM,KAAK;;;;;;;;;;;;;;;iDAWtD,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM,KAAK,GAAG,IAAI;sCACtB,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;gCAAC,SAAS,KAAK,KAAK;;oCACnC,KAAK,IAAI,kBAAI,6LAAC,KAAK,IAAI;;;;;kDACxB,6LAAC;kDAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAvCH,KAAK,KAAK;;;;;gBA6CpC;;;;;;;;;;;;;;;;AAKV;GAzEgB;KAAA", "debugId": null}}, {"offset": {"line": 1469, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Avatar({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn('aspect-square size-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn('bg-muted flex size-full items-center justify-center rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EAAE,SAAS,EAAE,GAAG,OAA0D;IACxF,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGf;KARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;QACjF,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1531, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu';\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction DropdownMenu({ ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />;\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />;\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return <DropdownMenuPrimitive.Trigger data-slot=\"dropdown-menu-trigger\" {...props} />;\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction DropdownMenuGroup({ ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />;\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = 'default',\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean;\r\n  variant?: 'default' | 'destructive';\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  );\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return <DropdownMenuPrimitive.RadioGroup data-slot=\"dropdown-menu-radio-group\" {...props} />;\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  );\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean;\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn('px-2 py-1.5 text-sm font-medium data-[inset]:pl-8', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn('bg-border -mx-1 my-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuShortcut({ className, ...props }: React.ComponentProps<'span'>) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn('text-muted-foreground ml-auto text-xs tracking-widest', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DropdownMenuSub({ ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />;\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean;\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        'focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8',\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  );\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EAAE,GAAG,OAAgE;IACzF,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAFS;AAIT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBAAO,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AACjF;MAJS;AAMT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBAAO,6LAAC,+KAAA,CAAA,UAA6B;QAAC,aAAU;QAAyB,GAAG,KAAK;;;;;;AACnF;MAJS;AAMT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EAAE,GAAG,OAAiE;IAC/F,qBAAO,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAC/E;MAFS;AAIT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBAAO,6LAAC,+KAAA,CAAA,aAAgC;QAAC,aAAU;QAA6B,GAAG,KAAK;;;;;;AAC1F;MAJS;AAMT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;QAClE,GAAG,KAAK;;;;;;AAGf;MAfS;AAiBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAAE,SAAS,EAAE,GAAG,OAAqC;IACjF,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;QACtE,GAAG,KAAK;;;;;;AAGf;OARS;AAUT,SAAS,gBAAgB,EAAE,GAAG,OAA+D;IAC3F,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAFS;AAIT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 1827, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app-components/nav-user.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useRouter } from 'next/navigation';\r\nimport { LogOutIcon, MoreVerticalIcon } from 'lucide-react';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from '@/components/ui/sidebar';\r\nimport axiosInstance from '@/lib/axios';\r\nimport { toast } from 'sonner';\r\n\r\nexport function NavUser({\r\n  user,\r\n}: {\r\n  user: {\r\n    name: string;\r\n    email: string;\r\n    avatar: string;\r\n  };\r\n}) {\r\n  const { isMobile } = useSidebar();\r\n  const router = useRouter();\r\n\r\n  // Handle logout action\r\n  const handleLogout = async () => {\r\n    const response = await axiosInstance.post('/auth-admin/logout', {});\r\n    if (response.data.success) {\r\n      router.push('/login');\r\n      toast('Logout Successfully');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <SidebarMenu>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <SidebarMenuButton\r\n              size=\"lg\"\r\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\"\r\n            >\r\n              <Avatar className=\"h-8 w-8 rounded-lg grayscale\">\r\n                <AvatarImage src={user.avatar} alt={user.name} />\r\n                <AvatarFallback className=\"rounded-lg\">UA</AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                <span className=\"truncate font-medium\">{user.name}</span>\r\n                <span className=\"truncate text-xs text-muted-foreground\">{user.email}</span>\r\n              </div>\r\n              <MoreVerticalIcon className=\"ml-auto size-4\" />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className=\"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg\"\r\n            side={isMobile ? 'bottom' : 'right'}\r\n            align=\"end\"\r\n            sideOffset={4}\r\n          >\r\n            <DropdownMenuLabel className=\"p-0 font-normal\">\r\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\r\n                <Avatar className=\"h-8 w-8 rounded-lg\">\r\n                  <AvatarImage src={user.avatar} alt={user.name} />\r\n                  <AvatarFallback className=\"rounded-lg\">UA</AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                  <span className=\"truncate font-medium\">{user.name}</span>\r\n                  <span className=\"truncate text-xs text-muted-foreground\">{user.email}</span>\r\n                </div>\r\n              </div>\r\n            </DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem onClick={handleLogout}>\r\n              <LogOutIcon />\r\n              Log out\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAQA;AAMA;AACA;;;AApBA;;;;;;;;AAsBO,SAAS,QAAQ,EACtB,IAAI,EAOL;;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,uBAAuB;IACvB,MAAM,eAAe;QACnB,MAAM,WAAW,MAAM,sHAAA,CAAA,UAAa,CAAC,IAAI,CAAC,sBAAsB,CAAC;QACjE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;YACzB,OAAO,IAAI,CAAC;YACZ,CAAA,GAAA,2IAAA,CAAA,QAAK,AAAD,EAAE;QACR;IACF;IAEA,qBACE,6LAAC,sIAAA,CAAA,cAAW;kBACV,cAAA,6LAAC,sIAAA,CAAA,kBAAe;sBACd,cAAA,6LAAC,+IAAA,CAAA,eAAY;;kCACX,6LAAC,+IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,6LAAC,qIAAA,CAAA,cAAW;4CAAC,KAAK,KAAK,MAAM;4CAAE,KAAK,KAAK,IAAI;;;;;;sDAC7C,6LAAC,qIAAA,CAAA,iBAAc;4CAAC,WAAU;sDAAa;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAwB,KAAK,IAAI;;;;;;sDACjD,6LAAC;4CAAK,WAAU;sDAA0C,KAAK,KAAK;;;;;;;;;;;;8CAEtE,6LAAC,iOAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGhC,6LAAC,+IAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,6LAAC,+IAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qIAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,MAAM;oDAAE,KAAK,KAAK,IAAI;;;;;;8DAC7C,6LAAC,qIAAA,CAAA,iBAAc;oDAAC,WAAU;8DAAa;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB,KAAK,IAAI;;;;;;8DACjD,6LAAC;oDAAK,WAAU;8DAA0C,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0CAI1E,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0CACtB,6LAAC,+IAAA,CAAA,mBAAgB;gCAAC,SAAS;;kDACzB,6LAAC,iNAAA,CAAA,aAAU;;;;;oCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5B;GArEgB;;QASO,sIAAA,CAAA,aAAU;QAChB,qIAAA,CAAA,YAAS;;;KAVV", "debugId": null}}, {"offset": {"line": 2072, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app-components/app-sidebar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport {\r\n  ActivitySquareIcon,\r\n  ClipboardListIcon,\r\n  LayoutDashboardIcon,\r\n  UserCheck,\r\n  Brain,\r\n  Star,\r\n  MessageSquareText,\r\n  Notebook,\r\n  Share2,\r\n  Settings,\r\n  FolderTree,\r\n  PenSquare,\r\n  Camera,\r\n  MessageSquareTextIcon,\r\n  Bell,\r\n  ShoppingBagIcon,\r\n} from 'lucide-react';\r\n\r\nimport { NavMain } from '@/app-components/nav-main';\r\nimport { NavUser } from '@/app-components/nav-user';\r\nimport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarHeader,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n} from '@/components/ui/sidebar';\r\nimport Image from 'next/image';\r\n\r\nconst data = {\r\n  user: {\r\n    name: 'uest admin',\r\n    email: '<EMAIL>',\r\n    avatar: '/avatars/shadcn.jpg',\r\n  },\r\n  navMain: [\r\n    {\r\n      title: 'Classes',\r\n      icon: LayoutDashboardIcon,\r\n      children: [\r\n        { title: 'Classes List', url: '/dashboard', icon: LayoutDashboardIcon },\r\n        { title: 'Testimonials', url: '/testimonials', icon: MessageSquareText },\r\n        { title: 'Thoughts', url: '/classes-thoughts', icon: Brain },\r\n        { title: 'Blogs', url: '/blog', icon: Notebook },\r\n\r\n      ]\r\n    },\r\n    {\r\n      title: 'Students',\r\n      icon: UserCheck,\r\n      children: [\r\n        { title: 'Student Details', url: '/student-details', icon: UserCheck },\r\n        { title: 'Reviews', url: '/reviews', icon: Star },\r\n      ]\r\n    },\r\n    {\r\n      title: 'Exam',\r\n      icon: ClipboardListIcon,\r\n      children: [\r\n        { title: 'Exam Details', url: '/exam-detail', icon: ClipboardListIcon },\r\n        { title: 'Question Bank', url: '/question-bank', icon: ActivitySquareIcon },\r\n        { title: 'Photo Monitoring', url: '/exam-monitoring', icon: Camera },\r\n        {title: 'Mock Exam Question Bank', url: '/mock-question-bank', icon: PenSquare},\r\n      ]\r\n    },\r\n    {\r\n      title: 'Referrals',\r\n      icon: Share2,\r\n      children: [\r\n        { title: 'Referral Management', url: '/referral-management', icon: Share2 },\r\n      ]\r\n    },\r\n    {\r\n      title: 'Constants',\r\n      icon: Settings,\r\n      children: [\r\n        { title: 'Manage Categories', url: '/constants', icon: FolderTree },\r\n      ]\r\n    },\r\n    {\r\n      title: 'Chats',\r\n      icon: MessageSquareTextIcon,\r\n      children: [\r\n        { title: 'Chats', url: '/chat', icon: MessageSquareTextIcon },\r\n      ]\r\n    },\r\n    {\r\n      title: 'Notifications',\r\n      icon: Bell,\r\n      children: [\r\n        { title: 'All Notifications', url: '/notifications', icon: Bell },\r\n      ]\r\n    },\r\n    {\r\n      title: 'Store',\r\n      icon: ShoppingBagIcon,\r\n      children: [\r\n        { title: 'Store', url: '/store', icon: ShoppingBagIcon },\r\n      ]\r\n    },\r\n  ],\r\n};\r\n\r\nexport function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {\r\n  return (\r\n    <Sidebar collapsible=\"offcanvas\" {...props}>\r\n      <SidebarHeader>\r\n        <SidebarMenu>\r\n          <SidebarMenuItem>\r\n            <SidebarMenuButton asChild className=\"data-[slot=sidebar-menu-button]:!p-1.5\">\r\n              <a href=\"#\">\r\n                <Image\r\n                  src=\"/logo.jpeg\"\r\n                  alt=\"App Logo\"\r\n                  width={130}\r\n                  height={40}\r\n                  className=\"rounded-md\"\r\n                />\r\n              </a>\r\n            </SidebarMenuButton>\r\n          </SidebarMenuItem>\r\n        </SidebarMenu>\r\n      </SidebarHeader>\r\n      <SidebarContent>\r\n        <NavMain items={data.navMain} />\r\n      </SidebarContent>\r\n      <SidebarFooter>\r\n        <NavUser user={data.user} />\r\n      </SidebarFooter>\r\n    </Sidebar>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA;AACA;AACA;AASA;AAjCA;;;;;;;AAmCA,MAAM,OAAO;IACX,MAAM;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IACA,SAAS;QACP;YACE,OAAO;YACP,MAAM,mOAAA,CAAA,sBAAmB;YACzB,UAAU;gBACR;oBAAE,OAAO;oBAAgB,KAAK;oBAAc,MAAM,mOAAA,CAAA,sBAAmB;gBAAC;gBACtE;oBAAE,OAAO;oBAAgB,KAAK;oBAAiB,MAAM,uOAAA,CAAA,oBAAiB;gBAAC;gBACvE;oBAAE,OAAO;oBAAY,KAAK;oBAAqB,MAAM,uMAAA,CAAA,QAAK;gBAAC;gBAC3D;oBAAE,OAAO;oBAAS,KAAK;oBAAS,MAAM,6MAAA,CAAA,WAAQ;gBAAC;aAEhD;QACH;QACA;YACE,OAAO;YACP,MAAM,mNAAA,CAAA,YAAS;YACf,UAAU;gBACR;oBAAE,OAAO;oBAAmB,KAAK;oBAAoB,MAAM,mNAAA,CAAA,YAAS;gBAAC;gBACrE;oBAAE,OAAO;oBAAW,KAAK;oBAAY,MAAM,qMAAA,CAAA,OAAI;gBAAC;aACjD;QACH;QACA;YACE,OAAO;YACP,MAAM,+NAAA,CAAA,oBAAiB;YACvB,UAAU;gBACR;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM,+NAAA,CAAA,oBAAiB;gBAAC;gBACtE;oBAAE,OAAO;oBAAiB,KAAK;oBAAkB,MAAM,iOAAA,CAAA,qBAAkB;gBAAC;gBAC1E;oBAAE,OAAO;oBAAoB,KAAK;oBAAoB,MAAM,yMAAA,CAAA,SAAM;gBAAC;gBACnE;oBAAC,OAAO;oBAA2B,KAAK;oBAAuB,MAAM,mNAAA,CAAA,YAAS;gBAAA;aAC/E;QACH;QACA;YACE,OAAO;YACP,MAAM,6MAAA,CAAA,SAAM;YACZ,UAAU;gBACR;oBAAE,OAAO;oBAAuB,KAAK;oBAAwB,MAAM,6MAAA,CAAA,SAAM;gBAAC;aAC3E;QACH;QACA;YACE,OAAO;YACP,MAAM,6MAAA,CAAA,WAAQ;YACd,UAAU;gBACR;oBAAE,OAAO;oBAAqB,KAAK;oBAAc,MAAM,qNAAA,CAAA,aAAU;gBAAC;aACnE;QACH;QACA;YACE,OAAO;YACP,MAAM,2OAAA,CAAA,wBAAqB;YAC3B,UAAU;gBACR;oBAAE,OAAO;oBAAS,KAAK;oBAAS,MAAM,2OAAA,CAAA,wBAAqB;gBAAC;aAC7D;QACH;QACA;YACE,OAAO;YACP,MAAM,qMAAA,CAAA,OAAI;YACV,UAAU;gBACR;oBAAE,OAAO;oBAAqB,KAAK;oBAAkB,MAAM,qMAAA,CAAA,OAAI;gBAAC;aACjE;QACH;QACA;YACE,OAAO;YACP,MAAM,2NAAA,CAAA,kBAAe;YACrB,UAAU;gBACR;oBAAE,OAAO;oBAAS,KAAK;oBAAU,MAAM,2NAAA,CAAA,kBAAe;gBAAC;aACxD;QACH;KACD;AACH;AAEO,SAAS,WAAW,EAAE,GAAG,OAA6C;IAC3E,qBACE,6LAAC,sIAAA,CAAA,UAAO;QAAC,aAAY;QAAa,GAAG,KAAK;;0BACxC,6LAAC,sIAAA,CAAA,gBAAa;0BACZ,cAAA,6LAAC,sIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC,sIAAA,CAAA,kBAAe;kCACd,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;4BAAC,OAAO;4BAAC,WAAU;sCACnC,cAAA,6LAAC;gCAAE,MAAK;0CACN,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOtB,6LAAC,sIAAA,CAAA,iBAAc;0BACb,cAAA,6LAAC,2IAAA,CAAA,UAAO;oBAAC,OAAO,KAAK,OAAO;;;;;;;;;;;0BAE9B,6LAAC,sIAAA,CAAA,gBAAa;0BACZ,cAAA,6LAAC,2IAAA,CAAA,UAAO;oBAAC,MAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;AAIhC;KA5BgB", "debugId": null}}, {"offset": {"line": 2329, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst badgeVariants = cva(\r\n  'inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\r\n        secondary:\r\n          'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\r\n        destructive:\r\n          'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : 'span';\r\n\r\n  return (\r\n    <Comp data-slot=\"badge\" className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OACuF;IAC1F,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QAAK,aAAU;QAAQ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAE3F;KAXS", "debugId": null}}, {"offset": {"line": 2381, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,6LAAC,8KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,8KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MAjBS;AAmBT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,8KAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf;OAVS", "debugId": null}}, {"offset": {"line": 2567, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/services/notificationService.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport interface AdminNotification {\r\n  id: string;\r\n  userId: string;\r\n  userType: 'ADMIN';\r\n  type: 'ADMIN_NEW_STUDENT_REGISTRATION' | 'ADMIN_NEW_CLASS_REGISTRATION' |\r\n        'ADMIN_PROFILE_REVIEW_REQUIRED' | 'ADMIN_CONTENT_REVIEW_REQUIRED' |\r\n        'NEW_EXAM_APPLICATION';\r\n  title: string;\r\n  message: string;\r\n  data?: any;\r\n  isRead: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface AdminNotificationPagination {\r\n  currentPage: number;\r\n  totalPages: number;\r\n  totalCount: number;\r\n  limit: number;\r\n  hasNextPage: boolean;\r\n  hasPrevPage: boolean;\r\n}\r\n\r\nexport interface AdminNotificationResponse {\r\n  notifications: AdminNotification[];\r\n  pagination: AdminNotificationPagination;\r\n}\r\n\r\n// Admin notification functions\r\nexport const getAdminNotifications = async (page: number = 1, limit: number = 10): Promise<AdminNotificationResponse> => {\r\n  try {\r\n    const response = await axiosInstance.get(`/notifications/admin?page=${page}&limit=${limit}`);\r\n    const data = response.data.data;\r\n\r\n    // Handle different response formats\r\n    if (Array.isArray(data)) {\r\n      // Old format - direct array\r\n      return {\r\n        notifications: data,\r\n        pagination: {\r\n          currentPage: 1,\r\n          totalPages: 1,\r\n          totalCount: data.length,\r\n          limit: data.length,\r\n          hasNextPage: false,\r\n          hasPrevPage: false\r\n        }\r\n      };\r\n    }\r\n\r\n    // New format - with pagination\r\n    return data || {\r\n      notifications: [],\r\n      pagination: {\r\n        currentPage: 1,\r\n        totalPages: 0,\r\n        totalCount: 0,\r\n        limit: 10,\r\n        hasNextPage: false,\r\n        hasPrevPage: false\r\n      }\r\n    };\r\n  } catch {\r\n    return {\r\n      notifications: [],\r\n      pagination: {\r\n        currentPage: 1,\r\n        totalPages: 0,\r\n        totalCount: 0,\r\n        limit: 10,\r\n        hasNextPage: false,\r\n        hasPrevPage: false\r\n      }\r\n    };\r\n  }\r\n};\r\n\r\n\r\n\r\nexport const getAdminUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/admin/count');\r\n  return response.data.data.count || 0;\r\n};\r\n\r\nexport const markAdminNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/admin/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllAdminNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/admin/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllAdminNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/admin/delete-all');\r\n  return response.data;\r\n};\r\n\r\n// Helper function to get notification action URL based on type and data\r\nexport const getNotificationActionUrl = (notification: AdminNotification): string => {\r\n  switch (notification.type) {\r\n    case 'ADMIN_NEW_STUDENT_REGISTRATION':\r\n      return `/student-details`;\r\n\r\n    case 'ADMIN_NEW_CLASS_REGISTRATION':\r\n      return `/dashboard`;\r\n\r\n    case 'ADMIN_PROFILE_REVIEW_REQUIRED':\r\n      if (notification.data?.studentId) {\r\n        return `/student-profile/${notification.data.studentId}`;\r\n      } else if (notification.data?.classId) {\r\n        return `/classes-details/${notification.data.classId}`;\r\n      }\r\n      return `/student-details`;\r\n\r\n    case 'ADMIN_CONTENT_REVIEW_REQUIRED':\r\n      if (notification.data?.blogId) {\r\n        return `/blog`;\r\n      } else if (notification.data?.testimonialId) {\r\n        return `/testimonials`;\r\n      } else if (notification.data?.type === 'blog') {\r\n        return `/blog`;\r\n      } else if (notification.data?.type === 'testimonial') {\r\n        return `/testimonials`;\r\n      }\r\n      return `/blog`;\r\n\r\n    case 'NEW_EXAM_APPLICATION':\r\n      if (notification.data?.redirectUrl) {\r\n        return notification.data.redirectUrl;\r\n      }\r\n      if (notification.data?.examId) {\r\n        return `/exam-applicant/${notification.data.examId}`;\r\n      }\r\n      return `/dashboard`;\r\n\r\n    default:\r\n      return '/dashboard';\r\n  }\r\n};\r\n\r\n// Helper function to get notification icon based on type\r\nexport const getNotificationIcon = (type: AdminNotification['type']): string => {\r\n  switch (type) {\r\n    case 'ADMIN_NEW_STUDENT_REGISTRATION':\r\n      return '👨‍🎓';\r\n    case 'ADMIN_NEW_CLASS_REGISTRATION':\r\n      return '🏫';\r\n    case 'ADMIN_PROFILE_REVIEW_REQUIRED':\r\n      return '📋';\r\n    case 'ADMIN_CONTENT_REVIEW_REQUIRED':\r\n      return '📝';\r\n    case 'NEW_EXAM_APPLICATION':\r\n      return '📝';\r\n    default:\r\n      return '🔔';\r\n  }\r\n};\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAgCO,MAAM,wBAAwB,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAC9E,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,KAAK,OAAO,EAAE,OAAO;QAC3F,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI;QAE/B,oCAAoC;QACpC,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,4BAA4B;YAC5B,OAAO;gBACL,eAAe;gBACf,YAAY;oBACV,aAAa;oBACb,YAAY;oBACZ,YAAY,KAAK,MAAM;oBACvB,OAAO,KAAK,MAAM;oBAClB,aAAa;oBACb,aAAa;gBACf;YACF;QACF;QAEA,+BAA+B;QAC/B,OAAO,QAAQ;YACb,eAAe,EAAE;YACjB,YAAY;gBACV,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,OAAO;gBACP,aAAa;gBACb,aAAa;YACf;QACF;IACF,EAAE,OAAM;QACN,OAAO;YACL,eAAe,EAAE;YACjB,YAAY;gBACV,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,OAAO;gBACP,aAAa;gBACb,aAAa;YACf;QACF;IACF;AACF;AAIO,MAAM,sBAAsB;IACjC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI;AACrC;AAEO,MAAM,8BAA8B,OAAO;IAChD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,+BAA+B,EAAE,gBAAgB;IAC5F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kCAAkC;IAC7C,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,8BAA8B;IACzC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,2BAA2B,CAAC;IACvC,OAAQ,aAAa,IAAI;QACvB,KAAK;YACH,OAAO,CAAC,gBAAgB,CAAC;QAE3B,KAAK;YACH,OAAO,CAAC,UAAU,CAAC;QAErB,KAAK;YACH,IAAI,aAAa,IAAI,EAAE,WAAW;gBAChC,OAAO,CAAC,iBAAiB,EAAE,aAAa,IAAI,CAAC,SAAS,EAAE;YAC1D,OAAO,IAAI,aAAa,IAAI,EAAE,SAAS;gBACrC,OAAO,CAAC,iBAAiB,EAAE,aAAa,IAAI,CAAC,OAAO,EAAE;YACxD;YACA,OAAO,CAAC,gBAAgB,CAAC;QAE3B,KAAK;YACH,IAAI,aAAa,IAAI,EAAE,QAAQ;gBAC7B,OAAO,CAAC,KAAK,CAAC;YAChB,OAAO,IAAI,aAAa,IAAI,EAAE,eAAe;gBAC3C,OAAO,CAAC,aAAa,CAAC;YACxB,OAAO,IAAI,aAAa,IAAI,EAAE,SAAS,QAAQ;gBAC7C,OAAO,CAAC,KAAK,CAAC;YAChB,OAAO,IAAI,aAAa,IAAI,EAAE,SAAS,eAAe;gBACpD,OAAO,CAAC,aAAa,CAAC;YACxB;YACA,OAAO,CAAC,KAAK,CAAC;QAEhB,KAAK;YACH,IAAI,aAAa,IAAI,EAAE,aAAa;gBAClC,OAAO,aAAa,IAAI,CAAC,WAAW;YACtC;YACA,IAAI,aAAa,IAAI,EAAE,QAAQ;gBAC7B,OAAO,CAAC,gBAAgB,EAAE,aAAa,IAAI,CAAC,MAAM,EAAE;YACtD;YACA,OAAO,CAAC,UAAU,CAAC;QAErB;YACE,OAAO;IACX;AACF;AAGO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 2700, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app-components/AdminNotificationBell.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { Bell } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\nimport {\r\n  getAdminNotifications,\r\n  getAdminUnreadCount,\r\n  markAdminNotificationAsRead,\r\n  markAllAdminNotificationsAsRead,\r\n  deleteAllAdminNotifications,\r\n  getNotificationActionUrl,\r\n  getNotificationIcon,\r\n  AdminNotification\r\n} from '@/services/notificationService';\r\nimport { toast } from 'sonner';\r\nimport { formatDistanceToNow } from 'date-fns';\r\nimport { useRouter } from 'next/navigation';\r\n\r\nexport default function AdminNotificationBell() {\r\n  const [notifications, setNotifications] = useState<AdminNotification[]>([]);\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n  const router = useRouter();\r\n\r\n  const safeNotifications = Array.isArray(notifications) ? notifications : [];\r\n\r\n  const fetchNotifications = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const [result, count] = await Promise.all([\r\n        getAdminNotifications(1, 20), \r\n        getAdminUnreadCount()\r\n      ]);\r\n\r\n      let notificationsList: AdminNotification[] = [];\r\n\r\n      if (Array.isArray(result)) {\r\n        notificationsList = result;\r\n      } else if (result?.notifications && Array.isArray(result.notifications)) {\r\n        notificationsList = result.notifications;\r\n      } else {\r\n        notificationsList = [];\r\n      }\r\n\r\n\r\n\r\n      setNotifications(notificationsList);\r\n      setUnreadCount(count || 0);\r\n    } catch (error) {\r\n      console.error('Error fetching admin notifications:', error);\r\n      toast.error('Failed to fetch notifications');\r\n      // Set empty arrays on error\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleMarkAsRead = async (notificationId: string) => {\r\n    try {\r\n      await markAdminNotificationAsRead(notificationId);\r\n\r\n      // Update local state\r\n      setNotifications(prev => {\r\n        if (!Array.isArray(prev)) return [];\r\n        return prev.map(notif =>\r\n          notif.id === notificationId ? { ...notif, isRead: true } : notif\r\n        );\r\n      });\r\n      setUnreadCount(prev => Math.max(0, prev - 1));\r\n    } catch (error) {\r\n      console.error('Error marking notification as read:', error);\r\n      toast.error('Failed to mark notification as read');\r\n    }\r\n  };\r\n\r\n  const handleMarkAllAsRead = async () => {\r\n    try {\r\n      await markAllAdminNotificationsAsRead();\r\n\r\n      // Update local state\r\n      setNotifications(prev => {\r\n        if (!Array.isArray(prev)) return [];\r\n        return prev.map(notif => ({ ...notif, isRead: true }));\r\n      });\r\n      setUnreadCount(0);\r\n      toast.success('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      toast.error('Failed to mark all notifications as read');\r\n    }\r\n  };\r\n\r\n  const handleRemoveAllClick = () => {\r\n    setShowDeleteDialog(true);\r\n  };\r\n\r\n  const handleConfirmRemoveAll = async () => {\r\n    setShowDeleteDialog(false);\r\n\r\n    try {\r\n      await deleteAllAdminNotifications();\r\n\r\n      // Update local state\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n      toast.success('All notifications removed successfully');\r\n    } catch (error) {\r\n      console.error('Error removing all notifications:', error);\r\n      toast.error('Failed to remove all notifications');\r\n    }\r\n  };\r\n\r\n  const handleNotificationClick = async (notification: AdminNotification) => {\r\n    // Mark as read if not already read\r\n    if (!notification.isRead) {\r\n      await handleMarkAsRead(notification.id);\r\n    }\r\n\r\n    // Navigate to relevant page\r\n    const actionUrl = getNotificationActionUrl(notification);\r\n    setIsOpen(false);\r\n    router.push(actionUrl);\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchNotifications();\r\n\r\n    // Poll for new notifications every 30 seconds\r\n    const interval = setInterval(fetchNotifications, 30000);\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          className=\"relative group border-orange-500 hover:border-orange-400 bg-black rounded-full\"\r\n        >\r\n          <div className=\"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n          <div className=\"relative z-10\">\r\n            <Bell className=\"h-5 w-5 text-orange-500\" />\r\n            {unreadCount > 0 && (\r\n              <Badge \r\n                variant=\"destructive\" \r\n                className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\r\n              >\r\n                {unreadCount > 99 ? '99+' : unreadCount}\r\n              </Badge>\r\n            )}\r\n          </div>\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent className=\"w-96 p-0\" align=\"end\">\r\n        <div className=\"p-4 border-b\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h3 className=\"font-semibold\">Admin Notifications</h3>\r\n            <div className=\"flex gap-2\">\r\n              {unreadCount > 0 && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={handleMarkAllAsRead}\r\n                  className=\"text-xs\"\r\n                >\r\n                  Mark all read\r\n                </Button>\r\n              )}\r\n              {safeNotifications.length > 0 && unreadCount === 0 && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={handleRemoveAllClick}\r\n                  className=\"text-xs text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                >\r\n                  Remove all\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"h-96 overflow-y-auto\">\r\n          {loading ? (\r\n            <div className=\"p-4 text-center text-muted-foreground\">\r\n              Loading notifications...\r\n            </div>\r\n          ) : safeNotifications.length === 0 ? (\r\n            <div className=\"p-4 text-center text-muted-foreground\">\r\n              No notifications yet\r\n            </div>\r\n          ) : (\r\n            <div className=\"divide-y\">\r\n              {safeNotifications.map((notification) => (\r\n                <div\r\n                  key={notification.id}\r\n                  className={`p-4 cursor-pointer hover:bg-muted/50 transition-colors ${\r\n                    !notification.isRead ? 'bg-blue-50/50' : ''\r\n                  }`}\r\n                  onClick={() => handleNotificationClick(notification)}\r\n                >\r\n                  <div className=\"flex items-start gap-3\">\r\n                    <div className=\"text-lg\">\r\n                      {getNotificationIcon(notification.type)}\r\n                    </div>\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <p className=\"font-medium text-sm\">{notification.title}</p>\r\n                        {!notification.isRead && (\r\n                          <div className=\"w-2 h-2 rounded-full bg-blue-500\" />\r\n                        )}\r\n                      </div>\r\n                      <p className=\"text-sm text-muted-foreground mt-1\">\r\n                        {notification.message}\r\n                      </p>\r\n                      <p className=\"text-xs text-muted-foreground mt-2\">\r\n                        {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n        {safeNotifications.length > 0 && (\r\n          <div className=\"p-3 border-t bg-muted/30\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"w-full text-xs\"\r\n              onClick={() => {\r\n                setIsOpen(false);\r\n                router.push('/notifications');\r\n              }}\r\n            >\r\n              View All Notifications\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n\r\n    {/* Delete Confirmation Dialog */}\r\n    <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>Remove All Notifications</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            Are you sure you want to remove all notifications? This action cannot be undone.\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n          <AlertDialogAction\r\n            onClick={handleConfirmRemoveAll}\r\n            className=\"bg-red-600 hover:bg-red-700\"\r\n          >\r\n            Remove All\r\n          </AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAKA;AACA;AAUA;AAUA;AACA;AACA;;;AAjCA;;;;;;;;;;;AAmCe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,oBAAoB,MAAM,OAAO,CAAC,iBAAiB,gBAAgB,EAAE;IAE3E,MAAM,qBAAqB;QACzB,IAAI;YACF,WAAW;YACX,MAAM,CAAC,QAAQ,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACxC,CAAA,GAAA,yIAAA,CAAA,wBAAqB,AAAD,EAAE,GAAG;gBACzB,CAAA,GAAA,yIAAA,CAAA,sBAAmB,AAAD;aACnB;YAED,IAAI,oBAAyC,EAAE;YAE/C,IAAI,MAAM,OAAO,CAAC,SAAS;gBACzB,oBAAoB;YACtB,OAAO,IAAI,QAAQ,iBAAiB,MAAM,OAAO,CAAC,OAAO,aAAa,GAAG;gBACvE,oBAAoB,OAAO,aAAa;YAC1C,OAAO;gBACL,oBAAoB,EAAE;YACxB;YAIA,iBAAiB;YACjB,eAAe,SAAS;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,4BAA4B;YAC5B,iBAAiB,EAAE;YACnB,eAAe;QACjB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,CAAA,GAAA,yIAAA,CAAA,8BAA2B,AAAD,EAAE;YAElC,qBAAqB;YACrB,iBAAiB,CAAA;gBACf,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,OAAO,EAAE;gBACnC,OAAO,KAAK,GAAG,CAAC,CAAA,QACd,MAAM,EAAE,KAAK,iBAAiB;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,IAAI;YAE/D;YACA,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,CAAA,GAAA,yIAAA,CAAA,kCAA+B,AAAD;YAEpC,qBAAqB;YACrB,iBAAiB,CAAA;gBACf,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,OAAO,EAAE;gBACnC,OAAO,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,CAAC;YACtD;YACA,eAAe;YACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,uBAAuB;QAC3B,oBAAoB;IACtB;IAEA,MAAM,yBAAyB;QAC7B,oBAAoB;QAEpB,IAAI;YACF,MAAM,CAAA,GAAA,yIAAA,CAAA,8BAA2B,AAAD;YAEhC,qBAAqB;YACrB,iBAAiB,EAAE;YACnB,eAAe;YACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,0BAA0B,OAAO;QACrC,mCAAmC;QACnC,IAAI,CAAC,aAAa,MAAM,EAAE;YACxB,MAAM,iBAAiB,aAAa,EAAE;QACxC;QAEA,4BAA4B;QAC5B,MAAM,YAAY,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;QAC3C,UAAU;QACV,OAAO,IAAI,CAAC;IACd;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR;YAEA,8CAA8C;YAC9C,MAAM,WAAW,YAAY,oBAAoB;YACjD;mDAAO,IAAM,cAAc;;QAC7B;0CAAG,EAAE;IAEL,qBACE;;0BACE,6LAAC,+IAAA,CAAA,eAAY;gBAAC,MAAM;gBAAQ,cAAc;;kCAC1C,6LAAC,+IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,cAAc,mBACb,6LAAC,oIAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,WAAU;sDAET,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAMtC,6LAAC,+IAAA,CAAA,sBAAmB;wBAAC,WAAU;wBAAW,OAAM;;0CAC9C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAgB;;;;;;sDAC9B,6LAAC;4CAAI,WAAU;;gDACZ,cAAc,mBACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;gDAIF,kBAAkB,MAAM,GAAG,KAAK,gBAAgB,mBAC/C,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAOT,6LAAC;gCAAI,WAAU;0CACZ,wBACC,6LAAC;oCAAI,WAAU;8CAAwC;;;;;2CAGrD,kBAAkB,MAAM,KAAK,kBAC/B,6LAAC;oCAAI,WAAU;8CAAwC;;;;;yDAIvD,6LAAC;oCAAI,WAAU;8CACZ,kBAAkB,GAAG,CAAC,CAAC,6BACtB,6LAAC;4CAEC,WAAW,CAAC,uDAAuD,EACjE,CAAC,aAAa,MAAM,GAAG,kBAAkB,IACzC;4CACF,SAAS,IAAM,wBAAwB;sDAEvC,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,yIAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;kEAExC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFAAuB,aAAa,KAAK;;;;;;oEACrD,CAAC,aAAa,MAAM,kBACnB,6LAAC;wEAAI,WAAU;;;;;;;;;;;;0EAGnB,6LAAC;gEAAE,WAAU;0EACV,aAAa,OAAO;;;;;;0EAEvB,6LAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa,SAAS,GAAG;oEAAE,WAAW;gEAAK;;;;;;;;;;;;;;;;;;2CArB1E,aAAa,EAAE;;;;;;;;;;;;;;;4BA8B7B,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP,UAAU;wCACV,OAAO,IAAI,CAAC;oCACd;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6LAAC,8IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI1B,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQX;GA5PwB;;QAMP,qIAAA,CAAA,YAAS;;;KANF", "debugId": null}}]}