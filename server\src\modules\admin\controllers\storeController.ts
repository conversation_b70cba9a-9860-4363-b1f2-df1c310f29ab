import { Request, Response } from 'express';
import { sendSuccess, sendError } from '@/utils/response';
import * as storeService from '../services/storeService';

export const getAllStoreItems = async (req: Request, res: Response): Promise<void> => {
  try {
    const { category, status, search } = req.query;
    
    const items = await storeService.getAllStoreItems({
      category: category as string,
      status: status as string,
      search: search as string
    });
    
    sendSuccess(res, items, 'Store items retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to retrieve store items', 500);
  }
};

export const getStoreItemById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const item = await storeService.getStoreItemById(id);
    
    if (!item) {
      sendError(res, 'Store item not found', 404);
      return;
    }
    
    sendSuccess(res, item, 'Store item retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to retrieve store item', 500);
  }
};

export const createStoreItem = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, description, price, coinPrice, quantity, category, image } = req.body;
    
    if (!name || !description || !price || !coinPrice || quantity === undefined || !category) {
      sendError(res, 'Missing required fields', 400);
      return;
    }
    
    const newItem = await storeService.createStoreItem({
      name,
      description,
      price: parseFloat(price),
      coinPrice: parseInt(coinPrice),
      quantity: parseInt(quantity),
      category,
      image: image || null
    });
    
    sendSuccess(res, newItem, 'Store item created successfully', 201);
  } catch (error: any) {
    sendError(res, error.message || 'Failed to create store item', 500);
  }
};

export const updateStoreItem = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, description, price, coinPrice, quantity, category, image, status } = req.body;
    
    const existingItem = await storeService.getStoreItemById(id);
    if (!existingItem) {
      sendError(res, 'Store item not found', 404);
      return;
    }
    
    const updatedItem = await storeService.updateStoreItem(id, {
      name,
      description,
      price: price ? parseFloat(price) : undefined,
      coinPrice: coinPrice ? parseInt(coinPrice) : undefined,
      quantity: quantity !== undefined ? parseInt(quantity) : undefined,
      category,
      image,
      status
    });
    
    sendSuccess(res, updatedItem, 'Store item updated successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to update store item', 500);
  }
};

export const deleteStoreItem = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const existingItem = await storeService.getStoreItemById(id);
    if (!existingItem) {
      sendError(res, 'Store item not found', 404);
      return;
    }
    
    await storeService.deleteStoreItem(id);
    
    sendSuccess(res, null, 'Store item deleted successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to delete store item', 500);
  }
};

export const getStoreStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const stats = await storeService.getStoreStats();
    sendSuccess(res, stats, 'Store statistics retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to retrieve store statistics', 500);
  }
};
