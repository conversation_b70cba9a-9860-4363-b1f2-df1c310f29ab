<?php

namespace Fees\Repositories;

use Admission\Models\StudentAcademicInfo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Admission\Repositories\AdmissionRepository;
use Fees\Interfaces\StudentFeePaymentInterface;
use Fees\Models\StudentPayments;

class StudentFeePaymentRepository implements StudentFeePaymentInterface
{
    protected $studentPayments;
    protected $admissionRepository;

    public function __construct(
        StudentPayments $studentPayments,
        AdmissionRepository $admissionRepository
    ) {
        $this->studentPayments = $studentPayments;
        $this->admissionRepository = $admissionRepository;
    }

    public function payStudentFee($data)
    {
        $data['payment_status'] = config('constants.PAYMENT_STATUS.SUCCESS');
        $data['payment_date'] = date('Y-m-d');
        $data['taken_by'] = Auth::id();
        $this->studentPayments::create($data);
    }

    public function getAll($data)
    {
        $paymentLogs =  $this->studentPayments
            ->leftJoin('student_details_view', 'student_details_view.student_academic_id', '=', 'student_payment_details.student_id')

            ->when(!empty($data['start_date']), function ($query) use ($data) {
                return $query->whereDate('payment_date', '>=', $data['start_date']);
            })
            ->when(!empty($data['end_date']), function ($query) use ($data) {
                return $query->whereDate('payment_date', '<=', $data['end_date']);
            })
            ->when(!empty($data['department']), function ($query) use ($data) {
                return $query->where('student_details_view.department_id', $data['department']);
            })
            ->when(!empty($data['classroom']), function ($query) use ($data) {
                return $query->where('student_details_view.classroom_id', $data['classroom']);
            })
            ->when(!empty($data['student_id']), function ($query) use ($data) {
                return $query->where('student_id', $data['student_id']);
            })->where('student_details_view.year_id', getActiveYearId())
            ->select(
                'student_details_view.first_name',
                'student_details_view.last_name',
                'student_details_view.classroom_id as student_class_id',
                'student_details_view.department_id as student_department_id',
                'student_payment_details.*',
                DB::raw("CONCAT(student_details_view.first_name, ' ', student_details_view.last_name) AS student_full_name"),
                DB::raw("CONCAT(student_details_view.class_name, ' (', student_details_view.department_name, ')') AS student_class_info"),
            );

        $concatenatedColumns = ['student_full_name', 'student_class_info'];
        searchColumn($data->input('columns'), $paymentLogs,  $concatenatedColumns);
        orderColumn($data, $paymentLogs, 'student_payment_details.id');

        return $paymentLogs;
    }

    public function getStudentPaymentDatatable($list)
    {
        $totalCreditAmount = number_format($list->sum('paid_amount'), 2, '.', ',');

        return datatables()->of($list)
            ->addColumn('student_full_name', function ($data) {
                return "<a href=" . route('studentfee', $data->student_id) . ">" . $data->student_full_name . "</a>";
            })
            ->addColumn('student_class_info', function ($data) {
                return  $data->student_class_info;
            })
            ->addColumn('month_name', function ($data) {
                return $data->month_name;
            })
            ->addColumn('payment_date', function ($data) {
                return $data->payment_date;
            })
            ->addColumn('payment_mode', function ($data) {
                return $data->payment_mode;
            })
            ->addColumn('paid_amount', function ($data) {
                return $data->paid_amount;
            })
            ->addColumn('cheque_no', function ($data) {
                return ($data->cheque_no != "") ? $data->cheque_no : "N/A";
            })
            ->addColumn('reference_no', function ($data) {
                return ($data->reference_no != "") ? $data->reference_no : "N/A";
            })
            ->addColumn('payment_status', function ($data) {
                return payment_color($data->payment_status);
            })
            ->addColumn('transaction_id', function ($data) {
                return ($data->transaction_id != "") ? $data->transaction_id : "N/A";
            })
            ->addColumn('action', function ($data) {
                $button = '';
                $button .= '<a href="' . route('studentPaymentReceipt', $data->id) . '" class="btn">
                <i class="fas fa-eye"></i></a>';
                if (Auth::user()->getIsSuperAdmin()) {
                    $button .= '<button data-paymentID=' . $data->id . ' class="btn deletePayment">
                    <i class="fas fa-trash"></i></button>';
                }

                return $button;
            })->rawColumns(['student_full_name', 'payment_status', 'action'])
            ->with([
                'total_credit_amount' => $totalCreditAmount,
            ])
            ->toJson();
    }

    public function getStudentPaymentLogsByPaymentid($payid)
    {
        return  $this->studentPayments::find($payid);
    }

    public function getPaidFees($student_id, $installmentName)
    {
        return $this->studentPayments::where('student_id', $student_id)->where('installment_name', $installmentName)->select('payment_category')->get();
    }

    public function getStudentsFeesByClassroom($data)
    {

        $students = $this->admissionRepository->getAllStudentByClassAndDepartment($data);

        $feesData = [];
        if ($data['classroom_id'] != '' && $data['student_id'] == '') {
            foreach ($students as $student) {
                $feesData[$student->id] = $this->getFeesByStudent($student->id);
            }
        } else if ($data['classroom_id'] != '' && $data['student_id'] != '') {
            $feesData[$data['student_id']] = $this->getFeesByStudent($data['student_id']);
        }


        return $feesData;
    }

    public function getFeesByStudent($student_id)
    {
        $academicInfos = StudentAcademicInfo::with([
            'getClassroom:id,class_name',
            'getYear:id,year_name',
            'getClassroom.feesDetails.feeType:id,name,slug',
            'getClassroom.feesDetails.getCategoryFees.feesCategory:id,category_name,deleted_at',
        ])
            ->where('id', $student_id)
            ->get();

        $groupedData = [];

        foreach ($academicInfos as $info) {
            $student = $info->getStudent;
            $classroom = $info->getClassroom;
            $year = $info->getYear;

            foreach ($classroom->feesDetails as $feeDetail) {
                $feeType = $feeDetail->feeType;
                $feeTypeSlug = $feeType->name ?? 'PER_YEAR';

                foreach ($feeDetail->getCategoryFees as $categoryFee) {
                    $feesCategory = $categoryFee->feesCategory;
                    if (!$feesCategory || $feesCategory->deleted_at) continue;

                    $key = $student->id . '-' . $classroom->id;

                    if (!isset($groupedData[$key])) {
                        $groupedData[$key] = [
                            'id' => $student->id,
                            'class_id' => $classroom->id,
                            'year_id' => $year->id,
                            'year_name' => $year->year_name,
                            'paid_amount' => 0,
                            'total_amount' => 0,
                            'due_amount' => 0,
                            'fee_locked' => $feeDetail->lock,
                            'installment_data' => [],
                        ];
                    }

                    $installmentCount = match ($feeTypeSlug) {
                        'per_month' => 12,
                        'six_month' => 2,
                        'quarter' => 3,
                        'per_year' => 1,
                        default => 1,
                    };

                    $installmentAmount = round($categoryFee->amount / $installmentCount, 2);

                    for ($i = 1; $i <= $installmentCount; $i++) {
                        $groupedData[$key]['installment_data'][] = [
                            'installment_no' => $i,
                            'amount' => $installmentAmount,
                            'fees_category' => $feesCategory->category_name,
                            'fees_category_id' => $feesCategory->id,
                        ];
                    }

                    $groupedData[$key]['total_amount'] = bcadd($groupedData[$key]['total_amount'], $categoryFee->amount, 2);
                }
            }
        }

        return array_values($groupedData);
    }

    public function getPaidFeesLogs($request, $student_id, $installmentName)
    {
        $perPage = 10;
        $page = $request->query('page', 1);

        return $this->studentPayments::with('getUser')
            ->where('student_id', $student_id)->where('installment_name', $installmentName)
            ->paginate($perPage, ['*'], 'page', $page);
    }
}
