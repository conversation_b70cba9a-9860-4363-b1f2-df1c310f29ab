<div class="modal fade" id="sidebarIconsModal" tabindex="-1" role="dialog" aria-labelledby="sidebarIconsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sidebarIconsModalLabel">Modules</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="sidebar-modal-container">
                
                <!-- Module Grid -->
                <div class="modal-body">
                    <div class="row" id="sidebarItems">
                        <div class="col-md-12">
                            <div class="modal-search">
                                <input type="text" id="sidebarSearch" class="form-control ms-2 me-2" placeholder="Search modules...">
                            </div>
                        </div>
                       
                        <!-- Sidebar Items in Grid -->
                        <div class="col-3 col-md-2 text-center mb-3 pt-3">
                            <a href="{{ route('home') }}" class="p-2">
                                <i class="fas fa-tachometer-alt fa-2x"></i>
                                <p>Dashboard</p>
                            </a>
                        </div>      
                        <div class="col-3 col-md-2 text-center mb-3 pt-3">
                            <a href="{{ route('annualcalendarview') }}" class="p-2">
                                <i class="fas fa-calendar-week fa-2x"></i>
                                <p>Annual Calendar</p>
                            </a>
                        </div>
                        @can('read year')
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('years.index') }}" class="p-2">
                                    <i class="fas fa-calendar fa-2x"></i>
                                    <p>Year</p>
                                </a>
                            </div>
                        @endcan                    
                        @can('read department')
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('department.index') }}" class="p-2">
                                    <i class="fa fa-sitemap fa-2x"></i>
                                    <p>Department</p>
                                </a>
                            </div>
                        @endcan
                        @can('read classroom')
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('classroom.index') }}" class="p-2">
                                    <i class="fas fa-restroom fa-2x"></i>
                                    <p>Classroom</p>
                                </a>
                            </div>
                        @endcan
                        @can('read subject')
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('subject.index') }}" class="p-2">
                                    <i class="fas fa-book fa-2x"></i>
                                    <p>Subjects</p>
                                </a>
                            </div>
                        @endcan
                        @can('read resource')
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('resource.index') }}" class="p-2">
                                    <i class="fas fa-school fa-2x"></i>
                                    <p>Resources</p>
                                </a>
                            </div>
                        @endcan
                        @can('read timeslot')
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('timeslots.index') }}" class="p-2">
                                    <i class="fas fa-clock fa-2x"></i>
                                    <p>Timeslots</p>
                                </a>
                            </div>
                        @endcan
                        @can('read event')
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('event.index') }}" class="p-2">
                                    <i class="fas fa-candy-cane fa-2x"></i>
                                    <p>Events</p>
                                </a>
                            </div>
                        @endcan
                        @can('read holiday')
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('holiday.index') }}" class="p-2">
                                    <i class="fa fa-h-square fa-2x"></i>
                                    <p>Holiday</p>
                                </a>
                            </div>
                        @endcan
                        @can('read fees category')
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('feesmanager.category.index') }}" class="p-2">
                                    <i class="fa fa-sitemap fa-2x"></i>
                                    <p>Fees Category</p>
                                </a>
                            </div>
                        @endcan
                        @can('manage classroom fees')
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('feesmanager.classroom-wise-fee.index') }}" class="p-2">
                                    <i class="fas fa-restroom fa-2x"></i>
                                    <p>Classroom Fees</p>
                                </a>
                            </div>
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('enquiry.fee.setup.view') }}" class="p-2">
                                    <i class="fas fa-restroom fa-2x"></i>
                                    <p>Enquiry Fees</p>
                                </a>
                            </div>
                        @endcan

                        @can('read enquiry')
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('enquiry.index') }}"class="p-2">
                                    <i class="fas fa-question-circle fa-2x"></i>
                                    <p>Enquiry Management</p>
                                </a>
                            </div>
                        @endcan
                        @can('read student')
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('student.index') }}"class="p-2">
                                    <i class="fa fa-university fa-2x"></i>
                                    <p>Student Management</p>
                                </a>
                            </div>
                        @endcan
                        @can('read document')
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('documents.index') }}"class="p-2">
                                    <i class="fas fa-file-signature unique-doc-icon fa-2x"></i>
                                    <p>Document</p>
                                </a>
                            </div>
                        @endcan
                        @if ($classrooms->count() > 0)
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('student-attendance') }}"class="p-2">
                                    <i class="fas fa-chalkboard fa-2x"></i>
                                    <p>Student Attendance</p>
                                </a>
                            </div>
                        @endif

                        @can('manage student fees')
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('getAllStudentPaymentLogs') }}"class="p-2">
                                    <i class="fa fa-rupee-sign fa-2x"></i>
                                    <p>Fees Payments</p>
                                </a>
                            </div>
                        @endcan
                        @can('manage enquiry fees')
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('enquiry.payments') }}"class="p-2">
                                    <i class="fa fa-question-circle fa-2x"></i>
                                    <p>Enquiry Payments</p>
                                </a>
                            </div>
                        @endcan
                        @can('manage passbook')
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('passbook') }}"class="p-2">
                                    <i class="fa fa-file-invoice fa-2x"></i>
                                    <p>Passbook</p>
                                </a>
                            </div>
                        @endcan
                        @canany(['read circular'])
                            <div class="col-3 col-md-2 text-center mb-3 pt-3">
                                <a href="{{ route('circulars.index') }}"class="p-2">
                                    <i class="fas fa-newspaper fa-2x"></i>
                                    <p>Circulars</p>
                                </a>
                            </div>
                        @endcan
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>