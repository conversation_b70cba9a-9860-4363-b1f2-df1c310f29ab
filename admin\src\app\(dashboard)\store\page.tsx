"use client";

import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Package, Search, Filter, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/app-components/dataTable';
import { ColumnDef } from '@tanstack/react-table';
import { toast } from 'sonner';
import Image from 'next/image';
import * as storeApi from '@/services/storeApi';

// Types
type StoreItem = storeApi.StoreItem;

interface AddItemFormData {
  name: string;
  description: string;
  price: number;
  coinPrice: number;
  quantity: number;
  category: string;
  image: File | null;
}

const categories = [
  'Stationery',
  'Electronics',
  'Apparel',
  'Accessories',
  'Digital',
  'Books',
  'Sports',
  'Other'
];

const StorePage = () => {
  const [items, setItems] = useState<StoreItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<StoreItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<StoreItem | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [formData, setFormData] = useState<AddItemFormData>({
    name: '',
    description: '',
    price: 0,
    coinPrice: 0,
    quantity: 0,
    category: '',
    image: null
  });

  // Load store items on component mount
  useEffect(() => {
    loadStoreItems();
  }, []);

  // Test API connection
  const testAPI = async () => {
    try {
      const response = await fetch('/api/v1/admin/store/test', {
        credentials: 'include'
      });
      const data = await response.json();
      console.log('API Test Result:', data);
      toast.success('API is working: ' + data.message);
    } catch (error: any) {
      console.error('API Test Failed:', error);
      toast.error('API test failed: ' + error.message);
    }
  };

  const loadStoreItems = async () => {
    try {
      setLoading(true);
      console.log('Loading store items...');
      const data = await storeApi.getAllStoreItems();
      console.log('Store items loaded:', data);
      setItems(data);
    } catch (error: any) {
      console.error('Failed to load store items:', error);
      toast.error(error.message || 'Failed to load store items');
    } finally {
      setLoading(false);
    }
  };

  // Filter items based on search and category
  useEffect(() => {
    let filtered = items;

    if (searchQuery) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }

    setFilteredItems(filtered);
  }, [items, searchQuery, selectedCategory]);

  // Handle form submission
  const handleAddItem = async () => {
    if (!formData.name || !formData.description || !formData.category) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      console.log('Creating store item with data:', formData);
      const createData: storeApi.CreateStoreItemData = {
        name: formData.name,
        description: formData.description,
        price: formData.price,
        coinPrice: formData.coinPrice,
        quantity: formData.quantity,
        category: formData.category,
        image: formData.image ? URL.createObjectURL(formData.image) : undefined
      };

      console.log('Sending create request:', createData);
      const result = await storeApi.createStoreItem(createData);
      console.log('Create result:', result);

      setIsAddModalOpen(false);
      resetForm();
      toast.success('Item added successfully!');
      loadStoreItems(); // Reload items
    } catch (error: any) {
      console.error('Failed to add item:', error);
      toast.error(error.message || 'Failed to add item');
    }
  };

  // Handle edit item
  const handleEditItem = async () => {
    if (!selectedItem || !formData.name || !formData.description || !formData.category) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const updateData: storeApi.UpdateStoreItemData = {
        name: formData.name,
        description: formData.description,
        price: formData.price,
        coinPrice: formData.coinPrice,
        quantity: formData.quantity,
        category: formData.category,
        image: formData.image ? URL.createObjectURL(formData.image) : undefined
      };

      await storeApi.updateStoreItem(selectedItem.id, updateData);
      setIsEditModalOpen(false);
      setSelectedItem(null);
      resetForm();
      toast.success('Item updated successfully!');
      loadStoreItems(); // Reload items
    } catch (error: any) {
      toast.error(error.message || 'Failed to update item');
    }
  };

  // Handle delete item
  const handleDeleteItem = async (id: string) => {
    try {
      await storeApi.deleteStoreItem(id);
      toast.success('Item deleted successfully!');
      loadStoreItems(); // Reload items
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete item');
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      price: 0,
      coinPrice: 0,
      quantity: 0,
      category: '',
      image: null
    });
  };

  // Open edit modal
  const openEditModal = (item: StoreItem) => {
    setSelectedItem(item);
    setFormData({
      name: item.name,
      description: item.description,
      price: item.price,
      coinPrice: item.coinPrice,
      quantity: item.quantity,
      category: item.category,
      image: null
    });
    setIsEditModalOpen(true);
  };

  // Table columns
  const columns: ColumnDef<StoreItem>[] = [
    {
      accessorKey: 'image',
      header: 'Image',
      cell: ({ row }) => (
        <div className="w-12 h-12 relative">
          <Image
            src={row.original.image || '/logo.png'}
            alt={row.original.name}
            fill
            className="object-cover rounded-md"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = '/logo.png';
            }}
          />
        </div>
      ),
    },
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => (
        <div className="max-w-[200px]">
          <div className="font-medium truncate">{row.original.name}</div>
          <div className="text-sm text-muted-foreground truncate">
            {row.original.description}
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'category',
      header: 'Category',
      cell: ({ row }) => (
        <Badge variant="secondary">{row.original.category}</Badge>
      ),
    },
    {
      accessorKey: 'price',
      header: 'Price',
      cell: ({ row }) => (
        <div className="space-y-1">
          <div className="font-medium">₹{row.original.price}</div>
          <div className="text-sm text-orange-600 flex items-center gap-1">
            <Package className="w-3 h-3" />
            {row.original.coinPrice} coins
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'quantity',
      header: 'Stock',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <span className={`font-medium ${row.original.quantity === 0 ? 'text-red-500' : 'text-green-600'}`}>
            {row.original.quantity}
          </span>
          <Badge variant={row.original.quantity === 0 ? 'destructive' : 'default'}>
            {row.original.status}
          </Badge>
        </div>
      ),
    },
    {
      accessorKey: 'createdAt',
      header: 'Created',
      cell: ({ row }) => (
        <div className="text-sm text-muted-foreground">
          {new Date(row.original.createdAt).toLocaleDateString()}
        </div>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => openEditModal(row.original)}
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => handleDeleteItem(row.original.id)}
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Store Management</h1>
          <p className="text-muted-foreground">Manage your store items and inventory</p>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button onClick={testAPI} variant="outline">
            Test API
          </Button>
          <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
            <DialogTrigger asChild>
              <Button className="bg-primary hover:bg-primary/90">
                <Plus className="w-4 h-4 mr-2" />
                Add Item
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Add New Store Item</DialogTitle>
                <DialogDescription>
                  Fill in the details to add a new item to your store
                </DialogDescription>
              </DialogHeader>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
              {/* Left Column */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Item Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Enter item name"
                  />
                </div>

                <div>
                  <Label htmlFor="category">Category *</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) => setFormData({ ...formData, category: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="price">Price (₹) *</Label>
                  <Input
                    id="price"
                    type="number"
                    value={formData.price}
                    onChange={(e) => setFormData({ ...formData, price: Number(e.target.value) })}
                    placeholder="Enter price"
                  />
                </div>

                <div>
                  <Label htmlFor="coinPrice">Coin Price *</Label>
                  <Input
                    id="coinPrice"
                    type="number"
                    value={formData.coinPrice}
                    onChange={(e) => setFormData({ ...formData, coinPrice: Number(e.target.value) })}
                    placeholder="Enter coin price"
                  />
                </div>
              </div>

              {/* Right Column */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="quantity">Quantity *</Label>
                  <Input
                    id="quantity"
                    type="number"
                    value={formData.quantity}
                    onChange={(e) => setFormData({ ...formData, quantity: Number(e.target.value) })}
                    placeholder="Enter quantity"
                  />
                </div>

                <div>
                  <Label htmlFor="image">Product Image</Label>
                  <Input
                    id="image"
                    type="file"
                    accept="image/*"
                    onChange={(e) => setFormData({ ...formData, image: e.target.files?.[0] || null })}
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description *</Label>
                  <Input
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Enter item description"
                  />
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddItem}>
                Add Item
              </Button>
            </DialogFooter>
          </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{items.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Active Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {items.filter(item => item.status === 'ACTIVE').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Out of Stock</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {items.filter(item => item.quantity === 0).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(items.map(item => item.category)).size}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search items..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <CardTitle>Store Items ({filteredItems.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable columns={columns} data={filteredItems} />
        </CardContent>
      </Card>

      {/* Edit Item Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Store Item</DialogTitle>
            <DialogDescription>
              Update the details of the selected item
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
            {/* Left Column */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-name">Item Name *</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter item name"
                />
              </div>

              <div>
                <Label htmlFor="edit-category">Category *</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => setFormData({ ...formData, category: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="edit-price">Price (₹) *</Label>
                <Input
                  id="edit-price"
                  type="number"
                  value={formData.price}
                  onChange={(e) => setFormData({ ...formData, price: Number(e.target.value) })}
                  placeholder="Enter price"
                />
              </div>

              <div>
                <Label htmlFor="edit-coinPrice">Coin Price *</Label>
                <Input
                  id="edit-coinPrice"
                  type="number"
                  value={formData.coinPrice}
                  onChange={(e) => setFormData({ ...formData, coinPrice: Number(e.target.value) })}
                  placeholder="Enter coin price"
                />
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-quantity">Quantity *</Label>
                <Input
                  id="edit-quantity"
                  type="number"
                  value={formData.quantity}
                  onChange={(e) => setFormData({ ...formData, quantity: Number(e.target.value) })}
                  placeholder="Enter quantity"
                />
              </div>

              <div>
                <Label htmlFor="edit-image">Product Image</Label>
                <Input
                  id="edit-image"
                  type="file"
                  accept="image/*"
                  onChange={(e) => setFormData({ ...formData, image: e.target.files?.[0] || null })}
                />
                {selectedItem && (
                  <div className="mt-2">
                    <p className="text-sm text-muted-foreground">Current image:</p>
                    <div className="w-20 h-20 relative mt-1">
                      <Image
                        src={selectedItem.image || '/logo.png'}
                        alt={selectedItem.name}
                        fill
                        className="object-cover rounded-md"
                      />
                    </div>
                  </div>
                )}
              </div>

              <div>
                <Label htmlFor="edit-description">Description *</Label>
                <Input
                  id="edit-description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Enter item description"
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditItem}>
              Update Item
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StorePage;