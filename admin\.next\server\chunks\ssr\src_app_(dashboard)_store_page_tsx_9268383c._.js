module.exports = {

"[project]/src/app/(dashboard)/store/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
"use client";
;
;
const categories = [
    'Stationery',
    'Electronics',
    'Apparel',
    'Accessories',
    'Digital',
    'Books',
    'Sports',
    'Other'
];
// Mock data for demonstration
const mockStoreItems = [
    {
        id: '1',
        name: 'UEST Premium Notebook Set',
        description: 'High-quality notebooks perfect for students. Set of 5 notebooks with different subjects.',
        price: 299,
        coinPrice: 150,
        quantity: 50,
        category: 'Stationery',
        image: '/logo.png',
        status: 'active',
        createdAt: '2024-01-15T10:30:00Z'
    },
    {
        id: '2',
        name: 'Scientific Calculator',
        description: 'Advanced scientific calculator for mathematics and engineering students.',
        price: 899,
        coinPrice: 450,
        quantity: 25,
        category: 'Electronics',
        image: '/uwhizExam.png',
        status: 'active',
        createdAt: '2024-01-14T14:20:00Z'
    },
    {
        id: '3',
        name: 'UEST Branded T-Shirt',
        description: 'Comfortable cotton t-shirt with UEST logo. Available in multiple sizes.',
        price: 499,
        coinPrice: 250,
        quantity: 0,
        category: 'Apparel',
        image: '/exam-logo.jpg',
        status: 'inactive',
        createdAt: '2024-01-13T09:15:00Z'
    }
];
const StorePage = ()=>{
    const [items, setItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(mockStoreItems);
    const [filteredItems, setFilteredItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(mockStoreItems);
    const [isAddModalOpen, setIsAddModalOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isEditModalOpen, setIsEditModalOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedItem, setSelectedItem] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [searchQuery, setSearchQuery] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [selectedCategory, setSelectedCategory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('all');
    const [formData, setFormData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        name: '',
        description: '',
        price: 0,
        coinPrice: 0,
        quantity: 0,
        category: '',
        image: null
    });
    // Filter items based on search and category
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        let filtered = items;
        if (searchQuery) {
            filtered = filtered.filter((item)=>item.name.toLowerCase().includes(searchQuery.toLowerCase()) || item.description.toLowerCase().includes(searchQuery.toLowerCase()));
        }
        if (selectedCategory !== 'all') {
            filtered = filtered.filter((item)=>item.category === selectedCategory);
        }
        setFilteredItems(filtered);
    }, [
        items,
        searchQuery,
        selectedCategory
    ]);
    // Handle form submission
    const handleAddItem = ()=>{
        if (!formData.name || !formData.description || !formData.category) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error('Please fill in all required fields');
            return;
        }
        const newItem = {
            id: Date.now().toString(),
            name: formData.name,
            description: formData.description,
            price: formData.price,
            coinPrice: formData.coinPrice,
            quantity: formData.quantity,
            category: formData.category,
            image: formData.image ? URL.createObjectURL(formData.image) : '/logo.png',
            status: formData.quantity > 0 ? 'active' : 'inactive',
            createdAt: new Date().toISOString()
        };
        setItems([
            newItem,
            ...items
        ]);
        setIsAddModalOpen(false);
        resetForm();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success('Item added successfully!');
    };
    // Handle edit item
    const handleEditItem = ()=>{
        if (!selectedItem || !formData.name || !formData.description || !formData.category) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error('Please fill in all required fields');
            return;
        }
        const updatedItems = items.map((item)=>item.id === selectedItem.id ? {
                ...item,
                name: formData.name,
                description: formData.description,
                price: formData.price,
                coinPrice: formData.coinPrice,
                quantity: formData.quantity,
                category: formData.category,
                image: formData.image ? URL.createObjectURL(formData.image) : item.image,
                status: formData.quantity > 0 ? 'active' : 'inactive'
            } : item);
        setItems(updatedItems);
        setIsEditModalOpen(false);
        setSelectedItem(null);
        resetForm();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success('Item updated successfully!');
    };
    // Handle delete item
    const handleDeleteItem = (id)=>{
        setItems(items.filter((item)=>item.id !== id));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success('Item deleted successfully!');
    };
    // Reset form
    const resetForm = ()=>{
        setFormData({
            name: '',
            description: '',
            price: 0,
            coinPrice: 0,
            quantity: 0,
            category: '',
            image: null
        });
    };
    // Open edit modal
    const openEditModal = (item)=>{
        setSelectedItem(item);
        setFormData({
            name: item.name,
            description: item.description,
            price: item.price,
            coinPrice: item.coinPrice,
            quantity: item.quantity,
            category: item.category,
            image: null
        });
        setIsEditModalOpen(true);
    };
};
}}),

};

//# sourceMappingURL=src_app_%28dashboard%29_store_page_tsx_9268383c._.js.map