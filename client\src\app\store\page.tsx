"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { ShoppingBag, Star, Filter, Search, Coins, ShoppingCart, Plus, Minus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import { isStudentAuthenticated } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Mock data for store products
const mockProducts = [
  {
    id: "1",
    name: "UEST Premium Notebook Set",
    description: "High-quality notebooks perfect for students. Set of 5 notebooks with different subjects.",
    price: 299,
    coinPrice: 150,
    originalPrice: 399,
    image: "/logo.png",
    category: "Stationery",
    rating: 4.5,
    reviews: 128,
    inStock: true,
    featured: true,
    tags: ["bestseller", "student-essential"]
  },
  {
    id: "2",
    name: "UEST Study Planner 2024",
    description: "Comprehensive study planner to organize your academic year effectively.",
    price: 199,
    coinPrice: 100,
    originalPrice: 249,
    image: "/uest_coin.png",
    category: "Stationery",
    rating: 4.8,
    reviews: 89,
    inStock: true,
    featured: true,
    tags: ["new-arrival", "productivity"]
  },
  {
    id: "3",
    name: "UEST Branded T-Shirt",
    description: "Comfortable cotton t-shirt with UEST logo. Available in multiple sizes.",
    price: 499,
    coinPrice: 250,
    originalPrice: 699,
    image: "/exam-logo.jpg",
    category: "Apparel",
    rating: 4.3,
    reviews: 67,
    inStock: true,
    featured: false,
    tags: ["apparel", "branded"]
  },
  {
    id: "4",
    name: "Scientific Calculator",
    description: "Advanced scientific calculator for mathematics and engineering students.",
    price: 899,
    coinPrice: 450,
    originalPrice: 1199,
    image: "/uwhizExam.png",
    category: "Electronics",
    rating: 4.7,
    reviews: 234,
    inStock: true,
    featured: true,
    tags: ["electronics", "student-essential"]
  },
  {
    id: "5",
    name: "UEST Water Bottle",
    description: "Eco-friendly stainless steel water bottle with UEST branding.",
    price: 349,
    coinPrice: 175,
    originalPrice: 449,
    image: "/teacher-profile.jpg",
    category: "Accessories",
    rating: 4.4,
    reviews: 156,
    inStock: false,
    featured: false,
    tags: ["eco-friendly", "accessories"]
  },
  {
    id: "6",
    name: "Study Lamp LED",
    description: "Adjustable LED study lamp with multiple brightness levels and USB charging port.",
    price: 1299,
    coinPrice: 650,
    originalPrice: 1599,
    image: "/Certificate.png",
    category: "Electronics",
    rating: 4.6,
    reviews: 98,
    inStock: true,
    featured: false,
    tags: ["electronics", "study-aid"]
  },
  {
    id: "7",
    name: "UEST Mobile App",
    description: "Download our mobile app for better learning experience on the go.",
    price: 0,
    coinPrice: 0,
    originalPrice: 99,
    image: "/app-qr-code.png",
    category: "Digital",
    rating: 4.9,
    reviews: 1250,
    inStock: true,
    featured: true,
    tags: ["free", "mobile-app"]
  },
  {
    id: "8",
    name: "UEST Achievement Badge",
    description: "Digital achievement badges for completing courses and exams.",
    price: 50,
    coinPrice: 25,
    originalPrice: 100,
    image: "/Achiever.svg",
    category: "Digital",
    rating: 4.6,
    reviews: 567,
    inStock: true,
    featured: false,
    tags: ["achievement", "digital"]
  }
];

const categories = ["All", "Stationery", "Electronics", "Apparel", "Accessories", "Digital"];

interface CartItem {
  id: string;
  name: string;
  price: number;
  coinPrice: number;
  quantity: number;
  image: string;
}

const StorePage = () => {
  const [products] = useState(mockProducts);
  const [filteredProducts, setFilteredProducts] = useState(mockProducts);
  const [loading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [sortBy, setSortBy] = useState("featured");
  const [cart, setCart] = useState<CartItem[]>([]);
  const [showCart, setShowCart] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<"coins" | "money">("money");
  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);

  useEffect(() => {
    setIsStudentLoggedIn(isStudentAuthenticated());
  }, []);

  useEffect(() => {
    let filtered = products;

    // Filter by category
    if (selectedCategory !== "All") {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Sort products
    switch (sortBy) {
      case "price-low":
        filtered = [...filtered].sort((a, b) => a.price - b.price);
        break;
      case "price-high":
        filtered = [...filtered].sort((a, b) => b.price - a.price);
        break;
      case "rating":
        filtered = [...filtered].sort((a, b) => b.rating - a.rating);
        break;
      case "featured":
      default:
        filtered = [...filtered].sort((a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0));
        break;
    }

    setFilteredProducts(filtered);
  }, [products, selectedCategory, searchQuery, sortBy]);

  const addToCart = (product: any) => {
    if (!isStudentLoggedIn) {
      toast.error("Please login to add items to cart");
      return;
    }

    const existingItem = cart.find(item => item.id === product.id);
    if (existingItem) {
      setCart(cart.map(item =>
        item.id === product.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, {
        id: product.id,
        name: product.name,
        price: product.price,
        coinPrice: product.coinPrice,
        quantity: 1,
        image: product.image
      }]);
    }
    toast.success("Item added to cart!");
  };

  const removeFromCart = (productId: string) => {
    setCart(cart.filter(item => item.id !== productId));
    toast.success("Item removed from cart!");
  };

  const updateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity === 0) {
      removeFromCart(productId);
      return;
    }
    setCart(cart.map(item =>
      item.id === productId
        ? { ...item, quantity: newQuantity }
        : item
    ));
  };

  const getTotalPrice = () => {
    return cart.reduce((total, item) => {
      const price = paymentMethod === "coins" ? item.coinPrice : item.price;
      return total + (price * item.quantity);
    }, 0);
  };

  const handleCheckout = () => {
    if (cart.length === 0) {
      toast.error("Your cart is empty!");
      return;
    }

    toast.success(`Checkout initiated with ${paymentMethod === "coins" ? "UEST Coins" : "Money"}!`);
    // Here you would integrate with actual payment processing
  };

  return (
    <>
      <Header />
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Hero Section */}
        <div className="bg-white text-black py-16">
          <div className="container mx-auto px-4 text-center">
            <div className="flex items-center justify-center gap-3 mb-4">
              <ShoppingBag className="w-12 h-12" />
              <h1 className="text-4xl md:text-5xl font-bold">UEST Store</h1>
            </div>
            <p className="text-xl md:text-2xl mb-6 opacity-90">
              Premium educational products for students
            </p>
            <p className="text-lg opacity-80">
              Shop with UEST Coins or regular payment methods
            </p>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row gap-4 mb-8">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Category Filter */}
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full md:w-48">
                <Filter className="w-4 h-4 mr-2" />
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Sort */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="featured">Featured</SelectItem>
                <SelectItem value="price-low">Price: Low to High</SelectItem>
                <SelectItem value="price-high">Price: High to Low</SelectItem>
                <SelectItem value="rating">Highest Rated</SelectItem>
              </SelectContent>
            </Select>

            {/* Cart Button */}
            <Button
              onClick={() => setShowCart(true)}
              className="bg-customOrange hover:bg-orange-600 relative"
            >
              <ShoppingCart className="w-4 h-4 mr-2" />
              Cart
              {cart.length > 0 && (
                <Badge className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                  {cart.reduce((total, item) => total + item.quantity, 0)}
                </Badge>
              )}
            </Button>
          </div>

          {/* Payment Method Toggle */}
          <div className="flex items-center gap-4 mb-8 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <span className="font-medium text-gray-700 dark:text-gray-300">Payment Method:</span>
            <div className="flex gap-2">
              <Button
                variant={paymentMethod === "money" ? "default" : "outline"}
                onClick={() => setPaymentMethod("money")}
                className={paymentMethod === "money" ? "bg-customOrange hover:bg-orange-600" : ""}
              >
                💰 Money
              </Button>
              <Button
                variant={paymentMethod === "coins" ? "default" : "outline"}
                onClick={() => setPaymentMethod("coins")}
                className={paymentMethod === "coins" ? "bg-customOrange hover:bg-orange-600" : ""}
              >
                <Coins className="w-4 h-4 mr-1" />
                UEST Coins
              </Button>
            </div>
          </div>

          {/* Products Grid */}
          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, index) => (
                <Card key={index} className="overflow-hidden">
                  <Skeleton className="h-48 w-full" />
                  <CardContent className="p-4">
                    <Skeleton className="h-4 w-3/4 mb-2" />
                    <Skeleton className="h-3 w-full mb-2" />
                    <Skeleton className="h-3 w-2/3" />
                  </CardContent>
                  <CardFooter className="p-4">
                    <Skeleton className="h-10 w-full" />
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {filteredProducts.map((product) => (
                <Card key={product.id} className="overflow-hidden store-card-hover group border-0 shadow-md bg-white dark:bg-gray-800">
                  <div className="relative h-48 bg-gray-100 dark:bg-gray-700">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = "/placeholder-product.jpg";
                      }}
                    />
                    {product.featured && (
                      <Badge className="absolute top-2 left-2 bg-customOrange">
                        Featured
                      </Badge>
                    )}
                    {!product.inStock && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <Badge variant="destructive">Out of Stock</Badge>
                      </div>
                    )}
                  </div>

                  <CardContent className="p-4">
                    <h3 className="font-semibold text-lg mb-2 dark:text-white line-clamp-1">{product.name}</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-3 line-clamp-2">
                      {product.description}
                    </p>

                    <div className="flex items-center gap-2 mb-3">
                      <div className="flex items-center">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-sm font-medium ml-1">{product.rating}</span>
                      </div>
                      <span className="text-gray-400 text-sm">({product.reviews} reviews)</span>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="text-2xl font-bold text-customOrange">
                          {paymentMethod === "coins" ? (
                            <span className="flex items-center">
                              <Coins className="w-5 h-5 mr-1" />
                              {product.coinPrice}
                            </span>
                          ) : (
                            `₹${product.price}`
                          )}
                        </span>
                        <span className="text-gray-400 line-through text-sm">
                          {paymentMethod === "coins" ? (
                            <span className="flex items-center">
                              <Coins className="w-3 h-3 mr-1" />
                              {Math.floor(product.originalPrice / 2)}
                            </span>
                          ) : (
                            `₹${product.originalPrice}`
                          )}
                        </span>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {product.category}
                      </Badge>
                    </div>
                  </CardContent>

                  <CardFooter className="p-4 pt-0">
                    <Button
                      onClick={() => addToCart(product)}
                      disabled={!product.inStock}
                      className="w-full bg-customOrange hover:bg-orange-600 disabled:bg-gray-300"
                    >
                      {product.inStock ? (
                        <>
                          <ShoppingCart className="w-4 h-4 mr-2" />
                          Add to Cart
                        </>
                      ) : (
                        "Out of Stock"
                      )}
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}

          {filteredProducts.length === 0 && !loading && (
            <div className="text-center py-16">
              <ShoppingBag className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 dark:text-gray-300 mb-2">
                No products found
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                Try adjusting your search or filter criteria
              </p>
            </div>
          )}
        </div>

        {/* Shopping Cart Dialog */}
        <Dialog open={showCart} onOpenChange={setShowCart}>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <ShoppingCart className="w-5 h-5" />
                Shopping Cart ({cart.reduce((total, item) => total + item.quantity, 0)} items)
              </DialogTitle>
              <DialogDescription>
                Review your items before checkout
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              {cart.length === 0 ? (
                <div className="text-center py-8">
                  <ShoppingCart className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-500">Your cart is empty</p>
                </div>
              ) : (
                <>
                  {cart.map((item) => (
                    <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg">
                      <Image
                        src={item.image}
                        alt={item.name}
                        width={60}
                        height={60}
                        className="rounded object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = "/placeholder-product.jpg";
                        }}
                      />
                      <div className="flex-1">
                        <h4 className="font-medium">{item.name}</h4>
                        <p className="text-customOrange font-semibold">
                          {paymentMethod === "coins" ? (
                            <span className="flex items-center">
                              <Coins className="w-4 h-4 mr-1" />
                              {item.coinPrice}
                            </span>
                          ) : (
                            `₹${item.price}`
                          )}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                        >
                          <Minus className="w-3 h-3" />
                        </Button>
                        <span className="w-8 text-center">{item.quantity}</span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                        >
                          <Plus className="w-3 h-3" />
                        </Button>
                      </div>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => removeFromCart(item.id)}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}

                  <div className="border-t pt-4">
                    <div className="flex justify-between items-center text-lg font-semibold">
                      <span>Total:</span>
                      <span className="text-customOrange">
                        {paymentMethod === "coins" ? (
                          <span className="flex items-center">
                            <Coins className="w-5 h-5 mr-1" />
                            {getTotalPrice()}
                          </span>
                        ) : (
                          `₹${getTotalPrice()}`
                        )}
                      </span>
                    </div>
                  </div>
                </>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setShowCart(false)}>
                Continue Shopping
              </Button>
              {cart.length > 0 && (
                <Button onClick={handleCheckout} className="bg-customOrange hover:bg-orange-600">
                  Checkout
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      <Footer />
    </>
  );
};

export default StorePage;