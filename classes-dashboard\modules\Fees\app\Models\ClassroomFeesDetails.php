<?php

namespace Fees\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClassroomFeesDetails extends Model
{
    use HasFactory;
    public $table = 'classroom_fees_details';

    protected $fillable = [
        'classroom_id',
        'year_id',
        'lock',
        'fee_type_id',
    ];

    public function getCategoryFees()
    {
        return $this->hasMany(ClassroomCategoryFees::class, 'classroom_fees_details_id');
    }

    public function feeType()
    {
        return $this->belongsTo(FeeType::class, 'fee_type_id');
    }
}
