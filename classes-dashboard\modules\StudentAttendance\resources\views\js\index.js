$(function () {
    $("#datepicker-timetable").datepicker({
        constrainInput: true,
        showOn: "button",
        dateFormat: "dd-mm-yy",
        buttonText: "btn",
        minDate:new Date(startYearDate),
        maxDate:new Date(endYearDate),
        onSelect: function (dateStr) {
            let inputDate = moment(dateStr, "DD-MM-YYYY");
            let formattedDate = inputDate.format("DD-MM-YYYY");
            let dayName = inputDate.format("dddd");

            $("#fc-dom-1")
                .empty()
                .html(formattedDate + " (" + dayName + ")");

            checkHolidays(inputDate);

            $("#classroom_select").trigger("change");
        },
    });
    $(".ui-datepicker-trigger").addClass(
        "fc-today-button fc-button fc-button-primary"
    );
    $(".ui-datepicker-trigger").html(
        '<i class="nav-icon fas fa-calendar-week"></i>'
    );
});

function checkHolidays(inputDate) {
    let isSunday = inputDate.day() === 0;
    let isHoliday = attendanceRoute.holidays.includes(
        inputDate.format("DD-MM-YYYY")
    );

    if (isSunday) {
        $("#fc-dom-1").addClass("text-danger");
    } else {
        $("#fc-dom-1").removeClass("text-danger");
    }

    if (isHoliday) {
        $("#fc-dom-1").addClass("text-warning");
    } else {
        $("#fc-dom-1").removeClass("text-warning");
    }
}

$(document).on("click", ".previosDay", function () {
    updateDate(-1);
});

$(document).on("click", ".nextDay", function () {
    updateDate(1);
});

$(document).on("click", ".today", function () {
    today();
});

function updateDate(offset) {
    let inputDate = moment($("#datepicker-timetable").val(), "DD-MM-YYYY");
    let adjustedDate = inputDate.add(offset, "days");

    let formattedDate = adjustedDate.format("DD-MM-YYYY");
    let dayName = adjustedDate.format("dddd");

    $("#datepicker-timetable").val(formattedDate);
    $("#fc-dom-1")
        .empty()
        .html(formattedDate + " (" + dayName + ")");

    checkHolidays(adjustedDate);

    $("#classroom_select").trigger("change");
}

function today() {
    let currentDate = moment();
    let formattedDate = currentDate.format("DD-MM-YYYY");
    let dayName = currentDate.format("dddd");

    $("#datepicker-timetable").val(formattedDate);
    $("#fc-dom-1")
        .empty()
        .html(formattedDate + " (" + dayName + ")");

    checkHolidays(currentDate);

    $("#classroom_select").trigger("change");
}

$("#classroom_select").change(function () {
    var selectedOption = $(this).find("option:selected");
    var classroomId = selectedOption.val();
    var departmentId = selectedOption.data("department");
    var date = $("#datepicker-timetable").val();

    var filters =
        "?classroom_id=" +
        classroomId +
        "&department_id=" +
        departmentId +
        "&date=" +
        date;
    var params = $.extend({}, doAjax_params_default);
    params["url"] = attendanceRoute.students + filters;
    params["requestType"] = `GET`;
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        let isAdminOrHead = attendanceRoute.isAdminOrHead;

        if (result.students.length == 0) {
            $("#studentAttendance")
                .empty()
                .html("<h3 class='text-center'>No data found<h3>");
            return;
        }

        let table =
            '<div class="table-responsive"><table id="student_table" class="table display table-striped table-borderless">';
        table +=
            "<thead>" +
            "<tr>" +
            "<th width='15%'>Update Attendance</th>" +
            "<th width='15%'>Discipline Issue</th>" +
            "<th width='38%'>Student Name</th>" +
            "<th width='37%'>Contact No</th>" +
            "</tr>" +
            "</thead>";

        table += "<tbody>";

        let takeCount = 0;

        console.log(result)

        result.students.forEach((element) => {
            let attendanceStatus =
                element.present === 1
                    ? "<span class='badge badge-success'>Present</span>"
                    : element.present === 0
                    ? "<span class='badge badge-danger'>Absent</span>"
                    : element.present === 2
                    ? "<span class='badge bg-red'>Holiday</span>"
                    : "";
            let showDiscipline = element.present === 1 ? "" : "d-none";
            let disciplineIssue = element.discipline_issue ?? [];

            let leaveStatus =
                element.leave_status != null
                    ? "Leave : " + element.leave_status
                    : "";
            let isChecked = "";
            if (
                element.leave_status != null &&
                element.leave_status == "APPROVED"
            ) {
                isChecked = "disabled";
            } else if (element.present === 0) {
                isChecked = "";
            } else {
                isChecked = "checked";
            }

            if (element.present != null) {
                takeCount++;
            }

            let checkBox = `<input name="attendance[]" id="${element.sid}" type="checkbox"
                class="custom-control-input custom-control-input-danger custom-control-input-outline"
                value="${element.sid}" ${isChecked}>
                <label data-toggle="tooltip" data-placement="right" title="Remove tick if student absent" for="${element.sid}"
                class="custom-control-label">&nbsp;</label><p class="leaveStatus badge badge-info">${leaveStatus}</p>`;

            table +=
                "<tr>" +
                `<td>
                ${
                    attendanceStatus && !isAdminOrHead
                        ? attendanceStatus
                        : attendanceStatus + checkBox
                }
            </td>` +
                `<td >
                <div class=${showDiscipline}><select style="width : 100% !important" multiple class='change-discipline select2' data-student_id=${
                    element.sid
                }>
                    <option value="Uniform" ${
                        disciplineIssue.includes("Uniform") ? "selected" : ""
                    }>Uniform</option>
                    <option value="Hair Oil" ${
                        disciplineIssue.includes("Hair Oil") ? "selected" : ""
                    }>Hair Oil</option>
                    <option value="Nail Cut / Polish" ${
                        disciplineIssue.includes("Nail Cut / Polish")
                            ? "selected"
                            : ""
                    }>Nail Cut / Polish</option>
                        <option value="Id Card" ${
                            disciplineIssue.includes("Id Card")
                                ? "selected"
                                : ""
                        }>Id Card</option>
                    <option value="Mis-behaviour" ${
                        disciplineIssue.includes("Mis-behaviour")
                            ? "selected"
                            : ""
                    }>Mis-behaviour</option>
                </select></div>
            </td>` +
                `<td>${element.first_name} ${element.last_name}</td>` +
                `<td>${element.contact_no}</td>` +
                "</tr>";
        });

        table += "</tbody></table></div>";

        $("#studentAttendance").empty().html(table);

        if (takeCount > 0 && !isAdminOrHead) {
            $(".storeAttendance").hide();
            $(".markASHoliday").hide();
        } else {
            $(".storeAttendance").show();
            $(".markASHoliday").show();
        }
    };
    commonAjax(params);
});

$("#classroom_select").trigger("change");

$(document).on("click", ".attendancestatus", function () {
    var url = attendanceRoute.status;

    var params = $.extend({}, doAjax_params_default);
    params["url"] = url;
    params["requestType"] = `POST`;
    params["data"] = {
        status: $(this).val(),
        student_id: $(this).attr("data-id"),
        date: $("#datepicker-timetable").val(),
    };
    params["successCallbackFunction"] = function successCallbackFunction(data) {
        toastr.success(data.success);
        $("#classroom_select").trigger("change");
    };

    var calert = function calert() {
        commonAjax(params);
    };
    commonAlert(calert);
});

$(document).on("click", ".storeAttendance", function () {
    var url = attendanceRoute.store;

    var attendanceData = [];

    $('input[name="attendance[]"]').each(function () {
        var studentId = $(this).val();
        var present = $(this).is(":checked") ? 1 : 0;

        attendanceData.push({
            student_id: studentId,
            present: present,
        });
    });

    var params = $.extend({}, doAjax_params_default);
    params["url"] = url;
    params["requestType"] = `POST`;
    params["data"] = {
        attendanceData: attendanceData,
        date: $("#datepicker-timetable").val(),
    };
    params["successCallbackFunction"] = function successCallbackFunction(data) {
        toastr.success(data.success);
        $("#classroom_select").trigger("change");
    };

    var calert = function calert() {
        commonAjax(params);
    };
    commonAlert(calert);
});

$(document).on("click", ".markASHoliday", function () {
    var url = attendanceRoute.store;

    var attendanceData = [];

    $('input[name="attendance[]"]').each(function () {
        var studentId = $(this).val();
        var present = 2;

        attendanceData.push({
            student_id: studentId,
            present: present,
        });
    });

    var params = $.extend({}, doAjax_params_default);
    params["url"] = url;
    params["requestType"] = `POST`;
    params["data"] = {
        attendanceData: attendanceData,
        date: $("#datepicker-timetable").val(),
    };
    params["successCallbackFunction"] = function successCallbackFunction(data) {
        toastr.success(data.success);
        $("#classroom_select").trigger("change");
    };

    var calert = function calert() {
        commonAjax(params);
    };
    commonAlert(calert);
});

$(document).on("change", ".change-discipline", function () {
    var url = attendanceRoute.discipline;

    var params = $.extend({}, doAjax_params_default);
    params["url"] = url;
    params["requestType"] = `POST`;
    params["data"] = {
        student_id: $(this).attr("data-student_id"),
        date: $("#datepicker-timetable").val(),
        discipline: $(this).val(),
    };
    params["successCallbackFunction"] = function successCallbackFunction(data) {
        toastr.success(data.success);
    };

    commonAjax(params);
});

$(document).on("click", ".exportData", function () {
    var url = attendanceRoute.export;
    var data = {
        start_date: $("#start_date").val(),
        end_date: $("#end_date").val(),
        classroom_id: $("#classroom_export_select").val(),
    };

    if ($("#start_date").val() == "" || $("#start_date").val() == "" || $("#classroom_export_select").val() == "") {
        toastr.error("Please select all options to export");
        return;
    }

    exportData(url, data);
});

$(document).on("click", ".viewData", function () {
    var params = $.extend({}, doAjax_params_default);
    params["url"] = attendanceRoute.view;
    params["requestType"] = `GET`;
    params["data"] = {
        start_date: $("#start_date").val(),
        end_date: $("#end_date").val(),
        classroom_id: $("#classroom_export_select").val(),
    };

    if ($("#start_date").val() == "" || $("#start_date").val() == "" || $("#classroom_export_select").val() == "") {
        toastr.error("Please select all options to view");
        return;
    }

    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        $("#viewAttendance").html(result);
    };
    commonAjax(params);
});