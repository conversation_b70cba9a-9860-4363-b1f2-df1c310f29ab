/* [project]/src/app/globals.css [app-client] (css) */
@layer properties;
@layer theme, base, components, utilities;

@layer theme {
  :root, :host {
    --color-red-50: oklch(97.1% .013 17.38);
    --color-red-100: oklch(93.6% .032 17.717);
    --color-red-200: oklch(88.5% .062 18.334);
    --color-red-500: oklch(63.7% .237 25.331);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-700: oklch(50.5% .213 27.518);
    --color-red-800: oklch(44.4% .177 26.899);
    --color-red-900: oklch(39.6% .141 25.723);
    --color-orange-50: oklch(98% .016 73.684);
    --color-orange-100: oklch(95.4% .038 75.164);
    --color-orange-200: oklch(90.1% .076 70.697);
    --color-orange-300: oklch(83.7% .128 66.29);
    --color-orange-400: oklch(75% .183 55.934);
    --color-orange-500: oklch(70.5% .213 47.604);
    --color-orange-600: oklch(64.6% .222 41.116);
    --color-orange-700: oklch(55.3% .195 38.402);
    --color-orange-800: oklch(47% .157 37.304);
    --color-orange-900: oklch(40.8% .123 38.172);
    --color-yellow-50: oklch(98.7% .026 102.212);
    --color-yellow-100: oklch(97.3% .071 103.193);
    --color-yellow-200: oklch(94.5% .129 101.54);
    --color-yellow-400: oklch(85.2% .199 91.936);
    --color-yellow-500: oklch(79.5% .184 86.047);
    --color-yellow-600: oklch(68.1% .162 75.834);
    --color-yellow-700: oklch(55.4% .135 66.442);
    --color-yellow-800: oklch(47.6% .114 61.907);
    --color-yellow-900: oklch(42.1% .095 57.708);
    --color-green-50: oklch(98.2% .018 155.826);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-200: oklch(92.5% .084 155.995);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-green-700: oklch(52.7% .154 150.069);
    --color-green-800: oklch(44.8% .119 151.328);
    --color-green-900: oklch(39.3% .095 152.535);
    --color-blue-50: oklch(97% .014 254.604);
    --color-blue-100: oklch(93.2% .032 255.585);
    --color-blue-200: oklch(88.2% .059 254.128);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-700: oklch(48.8% .243 264.376);
    --color-blue-800: oklch(42.4% .199 265.638);
    --color-purple-100: oklch(94.6% .033 307.174);
    --color-purple-200: oklch(90.2% .063 306.703);
    --color-purple-600: oklch(55.8% .288 302.321);
    --color-purple-800: oklch(43.8% .218 303.724);
    --color-slate-200: oklch(92.9% .013 255.508);
    --color-slate-300: oklch(86.9% .022 252.894);
    --color-gray-50: oklch(98.5% .002 247.839);
    --color-gray-100: oklch(96.7% .003 264.542);
    --color-gray-200: oklch(92.8% .006 264.531);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-4xl: 56rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-9xl: 8rem;
    --text-9xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --tracking-wide: .025em;
    --tracking-widest: .1em;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-xs: .125rem;
    --radius-2xl: 1rem;
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --aspect-video: 16 / 9;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
    --color-border: var(--border);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }

  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: #0000;
    opacity: 1;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer utilities {
  .\@container\/card-header {
    container-type: inline-size;
    container-name: card-header;
  }

  .\@container\/main {
    container-type: inline-size;
    container-name: main;
  }

  .pointer-events-none {
    pointer-events: none;
  }

  .visible {
    visibility: visible;
  }

  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .sticky {
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }

  .-top-2 {
    top: calc(var(--spacing) * -2);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1 {
    top: calc(var(--spacing) * 1);
  }

  .top-1\.5 {
    top: calc(var(--spacing) * 1.5);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-3\.5 {
    top: calc(var(--spacing) * 3.5);
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-24 {
    top: calc(var(--spacing) * 24);
  }

  .top-\[50\%\] {
    top: 50%;
  }

  .-right-2 {
    right: calc(var(--spacing) * -2);
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-1 {
    right: calc(var(--spacing) * 1);
  }

  .right-2 {
    right: calc(var(--spacing) * 2);
  }

  .right-3 {
    right: calc(var(--spacing) * 3);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-1 {
    left: calc(var(--spacing) * 1);
  }

  .left-2 {
    left: calc(var(--spacing) * 2);
  }

  .left-3 {
    left: calc(var(--spacing) * 3);
  }

  .left-4 {
    left: calc(var(--spacing) * 4);
  }

  .left-\[50\%\] {
    left: 50%;
  }

  .z-10 {
    z-index: 10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-50 {
    z-index: 50;
  }

  .col-start-2 {
    grid-column-start: 2;
  }

  .row-span-2 {
    grid-row: span 2 / span 2;
  }

  .row-start-1 {
    grid-row-start: 1;
  }

  .container {
    width: 100%;
  }

  @media (width >= 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (width >= 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (width >= 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (width >= 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (width >= 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }

  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }

  .mx-3 {
    margin-inline: calc(var(--spacing) * 3);
  }

  .mx-3\.5 {
    margin-inline: calc(var(--spacing) * 3.5);
  }

  .mx-auto {
    margin-inline: auto;
  }

  .my-0\.5 {
    margin-block: calc(var(--spacing) * .5);
  }

  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }

  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }

  .ms-2 {
    margin-inline-start: calc(var(--spacing) * 2);
  }

  .ms-4 {
    margin-inline-start: calc(var(--spacing) * 4);
  }

  .me-3 {
    margin-inline-end: calc(var(--spacing) * 3);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }

  .mt-auto {
    margin-top: auto;
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }

  .-ml-1 {
    margin-left: calc(var(--spacing) * -1);
  }

  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }

  .ml-6 {
    margin-left: calc(var(--spacing) * 6);
  }

  .ml-auto {
    margin-left: auto;
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline {
    display: inline;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .table {
    display: table;
  }

  .table-caption {
    display: table-caption;
  }

  .table-cell {
    display: table-cell;
  }

  .table-row {
    display: table-row;
  }

  .aspect-square {
    aspect-ratio: 1;
  }

  .aspect-video {
    aspect-ratio: var(--aspect-video);
  }

  .size-2 {
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
  }

  .size-2\.5 {
    width: calc(var(--spacing) * 2.5);
    height: calc(var(--spacing) * 2.5);
  }

  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .size-7 {
    width: calc(var(--spacing) * 7);
    height: calc(var(--spacing) * 7);
  }

  .size-8 {
    width: calc(var(--spacing) * 8);
    height: calc(var(--spacing) * 8);
  }

  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }

  .size-full {
    width: 100%;
    height: 100%;
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-7 {
    height: calc(var(--spacing) * 7);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-24 {
    height: calc(var(--spacing) * 24);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-36 {
    height: calc(var(--spacing) * 36);
  }

  .h-48 {
    height: calc(var(--spacing) * 48);
  }

  .h-64 {
    height: calc(var(--spacing) * 64);
  }

  .h-96 {
    height: calc(var(--spacing) * 96);
  }

  .h-\[200px\] {
    height: 200px;
  }

  .h-\[calc\(100\%-1px\)\] {
    height: calc(100% - 1px);
  }

  .h-\[calc\(100vh-64px\)\] {
    height: calc(100vh - 64px);
  }

  .h-\[var\(--radix-select-trigger-height\)\] {
    height: var(--radix-select-trigger-height);
  }

  .h-auto {
    height: auto;
  }

  .h-full {
    height: 100%;
  }

  .h-px {
    height: 1px;
  }

  .h-screen {
    height: 100vh;
  }

  .h-svh {
    height: 100svh;
  }

  .max-h-\(--radix-dropdown-menu-content-available-height\) {
    max-height: var(--radix-dropdown-menu-content-available-height);
  }

  .max-h-\(--radix-select-content-available-height\) {
    max-height: var(--radix-select-content-available-height);
  }

  .max-h-80 {
    max-height: calc(var(--spacing) * 80);
  }

  .max-h-96 {
    max-height: calc(var(--spacing) * 96);
  }

  .max-h-\[80vh\] {
    max-height: 80vh;
  }

  .max-h-\[90vh\] {
    max-height: 90vh;
  }

  .max-h-\[200px\] {
    max-height: 200px;
  }

  .max-h-\[400px\] {
    max-height: 400px;
  }

  .min-h-0 {
    min-height: calc(var(--spacing) * 0);
  }

  .min-h-\[40px\] {
    min-height: 40px;
  }

  .min-h-\[60vh\] {
    min-height: 60vh;
  }

  .min-h-\[600px\] {
    min-height: 600px;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .min-h-svh {
    min-height: 100svh;
  }

  .w-\(--sidebar-width\) {
    width: var(--sidebar-width);
  }

  .w-0 {
    width: calc(var(--spacing) * 0);
  }

  .w-1 {
    width: calc(var(--spacing) * 1);
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-3\/4 {
    width: 75%;
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-32 {
    width: calc(var(--spacing) * 32);
  }

  .w-40 {
    width: calc(var(--spacing) * 40);
  }

  .w-64 {
    width: calc(var(--spacing) * 64);
  }

  .w-80 {
    width: calc(var(--spacing) * 80);
  }

  .w-96 {
    width: calc(var(--spacing) * 96);
  }

  .w-\[--radix-dropdown-menu-trigger-width\] {
    width: --radix-dropdown-menu-trigger-width;
  }

  .w-\[95vw\] {
    width: 95vw;
  }

  .w-\[100px\] {
    width: 100px;
  }

  .w-\[130px\] {
    width: 130px;
  }

  .w-\[150px\] {
    width: 150px;
  }

  .w-\[200px\] {
    width: 200px;
  }

  .w-\[300px\] {
    width: 300px;
  }

  .w-\[460px\] {
    width: 460px;
  }

  .w-auto {
    width: auto;
  }

  .w-fit {
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .max-w-\(--skeleton-width\) {
    max-width: var(--skeleton-width);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-6xl {
    max-width: var(--container-6xl);
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-\[70\%\] {
    max-width: 70%;
  }

  .max-w-\[300px\] {
    max-width: 300px;
  }

  .max-w-\[400px\] {
    max-width: 400px;
  }

  .max-w-\[calc\(100\%-2rem\)\] {
    max-width: calc(100% - 2rem);
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-none {
    max-width: none;
  }

  .max-w-sm {
    max-width: var(--container-sm);
  }

  .max-w-xs {
    max-width: var(--container-xs);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-5 {
    min-width: calc(var(--spacing) * 5);
  }

  .min-w-8 {
    min-width: calc(var(--spacing) * 8);
  }

  .min-w-9 {
    min-width: calc(var(--spacing) * 9);
  }

  .min-w-10 {
    min-width: calc(var(--spacing) * 10);
  }

  .min-w-56 {
    min-width: calc(var(--spacing) * 56);
  }

  .min-w-\[8rem\] {
    min-width: 8rem;
  }

  .min-w-\[var\(--radix-select-trigger-width\)\] {
    min-width: var(--radix-select-trigger-width);
  }

  .flex-1 {
    flex: 1;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .shrink-0 {
    flex-shrink: 0;
  }

  .caption-bottom {
    caption-side: bottom;
  }

  .border-collapse {
    border-collapse: collapse;
  }

  .origin-\(--radix-dropdown-menu-content-transform-origin\) {
    transform-origin: var(--radix-dropdown-menu-content-transform-origin);
  }

  .origin-\(--radix-select-content-transform-origin\) {
    transform-origin: var(--radix-select-content-transform-origin);
  }

  .origin-\(--radix-tooltip-content-transform-origin\) {
    transform-origin: var(--radix-tooltip-content-transform-origin);
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-px {
    --tw-translate-x: -1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-px {
    --tw-translate-x: 1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-0\.5 {
    --tw-translate-y: calc(var(--spacing) * .5);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[calc\(-50\%_-_2px\)\] {
    --tw-translate-y: calc(-50% - 2px);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .rotate-45 {
    rotate: 45deg;
  }

  .transform {
    transform: var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
  }

  .animate-in {
    animation: enter var(--tw-duration, .15s) var(--tw-ease, ease);
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .cursor-default {
    cursor: default;
  }

  .cursor-not-allowed {
    cursor: not-allowed;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .scroll-my-1 {
    scroll-margin-block: calc(var(--spacing) * 1);
  }

  .list-disc {
    list-style-type: disc;
  }

  .auto-rows-min {
    grid-auto-rows: min-content;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .grid-rows-\[auto_auto\] {
    grid-template-rows: auto auto;
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-col-reverse {
    flex-direction: column-reverse;
  }

  .flex-row {
    flex-direction: row;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .items-start {
    align-items: flex-start;
  }

  .items-stretch {
    align-items: stretch;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  :where(.space-y-0 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-12 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 12) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-5 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 5) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.divide-y > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }

  .self-start {
    align-self: flex-start;
  }

  .justify-self-end {
    justify-self: flex-end;
  }

  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .overflow-auto {
    overflow: auto;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .overscroll-contain {
    overscroll-behavior: contain;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-\[2px\] {
    border-radius: 2px;
  }

  .rounded-\[4px\] {
    border-radius: 4px;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius);
  }

  .rounded-md {
    border-radius: calc(var(--radius)  - 2px);
  }

  .rounded-none {
    border-radius: 0;
  }

  .rounded-sm {
    border-radius: calc(var(--radius)  - 4px);
  }

  .rounded-xl {
    border-radius: calc(var(--radius)  + 4px);
  }

  .rounded-xs {
    border-radius: var(--radius-xs);
  }

  .rounded-tl-lg {
    border-top-left-radius: var(--radius);
  }

  .rounded-br-md {
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .rounded-bl-md {
    border-bottom-left-radius: calc(var(--radius)  - 2px);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-\[1\.5px\] {
    border-style: var(--tw-border-style);
    border-width: 1.5px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-t-2 {
    border-top-style: var(--tw-border-style);
    border-top-width: 2px;
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }

  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-l-2 {
    border-left-style: var(--tw-border-style);
    border-left-width: 2px;
  }

  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }

  .border-\(--color-border\) {
    border-color: var(--color-border);
  }

  .border-\[hsl\(var\(--border\)\)\] {
    border-color: hsl(var(--border));
  }

  .border-black {
    border-color: var(--color-black);
  }

  .border-blue-200 {
    border-color: var(--color-blue-200);
  }

  .border-border\/50 {
    border-color: var(--border);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-border\/50 {
      border-color: color-mix(in oklab, var(--border) 50%, transparent);
    }
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-gray-600 {
    border-color: var(--color-gray-600);
  }

  .border-green-200 {
    border-color: var(--color-green-200);
  }

  .border-green-500 {
    border-color: var(--color-green-500);
  }

  .border-green-600 {
    border-color: var(--color-green-600);
  }

  .border-input {
    border-color: var(--input);
  }

  .border-orange-200 {
    border-color: var(--color-orange-200);
  }

  .border-orange-300 {
    border-color: var(--color-orange-300);
  }

  .border-orange-500 {
    border-color: var(--color-orange-500);
  }

  .border-primary {
    border-color: var(--primary);
  }

  .border-purple-200 {
    border-color: var(--color-purple-200);
  }

  .border-red-200 {
    border-color: var(--color-red-200);
  }

  .border-red-500 {
    border-color: var(--color-red-500);
  }

  .border-red-600 {
    border-color: var(--color-red-600);
  }

  .border-sidebar-border {
    border-color: var(--sidebar-border);
  }

  .border-transparent {
    border-color: #0000;
  }

  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }

  .border-yellow-600 {
    border-color: var(--color-yellow-600);
  }

  .bg-\(--color-bg\) {
    background-color: var(--color-bg);
  }

  .bg-\[\#FD904B\] {
    background-color: #fd904b;
  }

  .bg-\[\#ff914d\] {
    background-color: #ff914d;
  }

  .bg-\[\#fff8f3\] {
    background-color: #fff8f3;
  }

  .bg-accent {
    background-color: var(--accent);
  }

  .bg-background {
    background-color: var(--background);
  }

  .bg-black {
    background-color: var(--color-black);
  }

  .bg-black\/50 {
    background-color: #00000080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/50 {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .bg-blue-50\/50 {
    background-color: #eff6ff80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-50\/50 {
      background-color: color-mix(in oklab, var(--color-blue-50) 50%, transparent);
    }
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-border {
    background-color: var(--border);
  }

  .bg-card {
    background-color: var(--card);
  }

  .bg-destructive {
    background-color: var(--destructive);
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }

  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-200 {
    background-color: var(--color-green-200);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-green-600 {
    background-color: var(--color-green-600);
  }

  .bg-muted {
    background-color: var(--muted);
  }

  .bg-muted\/30 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/30 {
      background-color: color-mix(in oklab, var(--muted) 30%, transparent);
    }
  }

  .bg-muted\/50 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/50 {
      background-color: color-mix(in oklab, var(--muted) 50%, transparent);
    }
  }

  .bg-orange-100 {
    background-color: var(--color-orange-100);
  }

  .bg-orange-200 {
    background-color: var(--color-orange-200);
  }

  .bg-orange-400 {
    background-color: var(--color-orange-400);
  }

  .bg-orange-500 {
    background-color: var(--color-orange-500);
  }

  .bg-popover {
    background-color: var(--popover);
  }

  .bg-primary {
    background-color: var(--primary);
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-red-200 {
    background-color: var(--color-red-200);
  }

  .bg-red-500 {
    background-color: var(--color-red-500);
  }

  .bg-red-600 {
    background-color: var(--color-red-600);
  }

  .bg-secondary {
    background-color: var(--secondary);
  }

  .bg-sidebar {
    background-color: var(--sidebar);
  }

  .bg-sidebar-border {
    background-color: var(--sidebar-border);
  }

  .bg-slate-200 {
    background-color: var(--color-slate-200);
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }

  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }

  .bg-yellow-200 {
    background-color: var(--color-yellow-200);
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-orange-50 {
    --tw-gradient-from: var(--color-orange-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-white {
    --tw-gradient-to: var(--color-white);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .fill-current {
    fill: currentColor;
  }

  .fill-primary {
    fill: var(--primary);
  }

  .fill-yellow-400 {
    fill: var(--color-yellow-400);
  }

  .fill-yellow-500 {
    fill: var(--color-yellow-500);
  }

  .object-contain {
    object-fit: contain;
  }

  .object-cover {
    object-fit: cover;
  }

  .p-0 {
    padding: calc(var(--spacing) * 0);
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-12 {
    padding: calc(var(--spacing) * 12);
  }

  .p-16 {
    padding: calc(var(--spacing) * 16);
  }

  .p-\[3px\] {
    padding: 3px;
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }

  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }

  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }

  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }

  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }

  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }

  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }

  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }

  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }

  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }

  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }

  .pl-12 {
    padding-left: calc(var(--spacing) * 12);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .align-middle {
    vertical-align: middle;
  }

  .font-mono {
    font-family: var(--font-geist-mono);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-9xl {
    font-size: var(--text-9xl);
    line-height: var(--tw-leading, var(--text-9xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .text-\[0\.8rem\] {
    font-size: .8rem;
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-extrabold {
    --tw-font-weight: var(--font-weight-extrabold);
    font-weight: var(--font-weight-extrabold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }

  .tracking-widest {
    --tw-tracking: var(--tracking-widest);
    letter-spacing: var(--tracking-widest);
  }

  .text-balance {
    text-wrap: balance;
  }

  .break-words {
    overflow-wrap: break-word;
  }

  .whitespace-normal {
    white-space: normal;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .text-\[\#FD904B\] {
    color: #fd904b;
  }

  .text-\[hsl\(var\(--foreground\)\)\] {
    color: hsl(var(--foreground));
  }

  .text-accent-foreground {
    color: var(--accent-foreground);
  }

  .text-black {
    color: var(--color-black);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-blue-800 {
    color: var(--color-blue-800);
  }

  .text-card-foreground {
    color: var(--card-foreground);
  }

  .text-current {
    color: currentColor;
  }

  .text-destructive {
    color: var(--destructive);
  }

  .text-foreground {
    color: var(--foreground);
  }

  .text-gray-200 {
    color: var(--color-gray-200);
  }

  .text-gray-300 {
    color: var(--color-gray-300);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-500 {
    color: var(--color-green-500);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-green-700 {
    color: var(--color-green-700);
  }

  .text-green-800 {
    color: var(--color-green-800);
  }

  .text-muted-foreground {
    color: var(--muted-foreground);
  }

  .text-orange-400 {
    color: var(--color-orange-400);
  }

  .text-orange-500 {
    color: var(--color-orange-500);
  }

  .text-orange-600 {
    color: var(--color-orange-600);
  }

  .text-orange-800 {
    color: var(--color-orange-800);
  }

  .text-popover-foreground {
    color: var(--popover-foreground);
  }

  .text-primary {
    color: var(--primary);
  }

  .text-primary-foreground {
    color: var(--primary-foreground);
  }

  .text-purple-600 {
    color: var(--color-purple-600);
  }

  .text-purple-800 {
    color: var(--color-purple-800);
  }

  .text-red-500 {
    color: var(--color-red-500);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-red-700 {
    color: var(--color-red-700);
  }

  .text-red-800 {
    color: var(--color-red-800);
  }

  .text-secondary-foreground {
    color: var(--secondary-foreground);
  }

  .text-sidebar-foreground {
    color: var(--sidebar-foreground);
  }

  .text-sidebar-foreground\/70 {
    color: var(--sidebar-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-sidebar-foreground\/70 {
      color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);
    }
  }

  .text-white {
    color: var(--color-white);
  }

  .text-white\/80 {
    color: #fffc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/80 {
      color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }

  .text-yellow-400 {
    color: var(--color-yellow-400);
  }

  .text-yellow-500 {
    color: var(--color-yellow-500);
  }

  .text-yellow-600 {
    color: var(--color-yellow-600);
  }

  .text-yellow-700 {
    color: var(--color-yellow-700);
  }

  .text-yellow-800 {
    color: var(--color-yellow-800);
  }

  .capitalize {
    text-transform: capitalize;
  }

  .tabular-nums {
    --tw-numeric-spacing: tabular-nums;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
  }

  .underline {
    text-decoration-line: underline;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-70 {
    opacity: .7;
  }

  .opacity-100 {
    opacity: 1;
  }

  .shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-sidebar-ring {
    --tw-ring-color: var(--sidebar-ring);
  }

  .ring-offset-background {
    --tw-ring-offset-color: var(--background);
  }

  .outline-hidden {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .outline-hidden {
      outline: 2px solid #0000;
      outline-offset: 2px;
    }
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .grayscale {
    --tw-grayscale: grayscale(100%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[color\,box-shadow\] {
    transition-property: color, box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[left\,right\,width\] {
    transition-property: left, right, width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[margin\,opacity\] {
    transition-property: margin, opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\,height\,padding\] {
    transition-property: width, height, padding;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\,height\] {
    transition-property: width, height;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\] {
    transition-property: width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-none {
    transition-property: none;
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .ease-linear {
    --tw-ease: linear;
    transition-timing-function: linear;
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }

  .fade-in-0 {
    --tw-enter-opacity: 0;
  }

  .fade-in-50 {
    --tw-enter-opacity: .5;
  }

  .zoom-in-95 {
    --tw-enter-scale: .95;
  }

  .group-focus-within\/menu-item\:opacity-100:is(:where(.group\/menu-item):focus-within *) {
    opacity: 1;
  }

  @media (hover: hover) {
    .group-hover\:opacity-20:is(:where(.group):hover *) {
      opacity: .2;
    }
  }

  @media (hover: hover) {
    .group-hover\/menu-item\:opacity-100:is(:where(.group\/menu-item):hover *) {
      opacity: 1;
    }
  }

  .group-has-data-\[collapsible\=icon\]\/sidebar-wrapper\:h-12:is(:where(.group\/sidebar-wrapper):has([data-collapsible="icon"]) *) {
    height: calc(var(--spacing) * 12);
  }

  .group-has-data-\[sidebar\=menu-action\]\/menu-item\:pr-8:is(:where(.group\/menu-item):has([data-sidebar="menu-action"]) *) {
    padding-right: calc(var(--spacing) * 8);
  }

  .group-data-\[collapsible\=icon\]\:-mt-8:is(:where(.group)[data-collapsible="icon"] *) {
    margin-top: calc(var(--spacing) * -8);
  }

  .group-data-\[collapsible\=icon\]\:hidden:is(:where(.group)[data-collapsible="icon"] *) {
    display: none;
  }

  .group-data-\[collapsible\=icon\]\:size-8\!:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--spacing) * 8) !important;
    height: calc(var(--spacing) * 8) !important;
  }

  .group-data-\[collapsible\=icon\]\:w-\(--sidebar-width-icon\):is(:where(.group)[data-collapsible="icon"] *) {
    width: var(--sidebar-width-icon);
  }

  .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\)\]:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--sidebar-width-icon)  + (calc(var(--spacing) * 4)));
  }

  .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\+2px\)\]:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--sidebar-width-icon)  + (calc(var(--spacing) * 4))  + 2px);
  }

  .group-data-\[collapsible\=icon\]\:overflow-hidden:is(:where(.group)[data-collapsible="icon"] *) {
    overflow: hidden;
  }

  .group-data-\[collapsible\=icon\]\:p-0\!:is(:where(.group)[data-collapsible="icon"] *) {
    padding: calc(var(--spacing) * 0) !important;
  }

  .group-data-\[collapsible\=icon\]\:p-2\!:is(:where(.group)[data-collapsible="icon"] *) {
    padding: calc(var(--spacing) * 2) !important;
  }

  .group-data-\[collapsible\=icon\]\:opacity-0:is(:where(.group)[data-collapsible="icon"] *) {
    opacity: 0;
  }

  .group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible="offcanvas"] *) {
    right: calc(var(--sidebar-width) * -1);
  }

  .group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible="offcanvas"] *) {
    left: calc(var(--sidebar-width) * -1);
  }

  .group-data-\[collapsible\=offcanvas\]\:w-0:is(:where(.group)[data-collapsible="offcanvas"] *) {
    width: calc(var(--spacing) * 0);
  }

  .group-data-\[collapsible\=offcanvas\]\:translate-x-0:is(:where(.group)[data-collapsible="offcanvas"] *) {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled="true"] *) {
    pointer-events: none;
  }

  .group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled="true"] *) {
    opacity: .5;
  }

  .group-data-\[side\=left\]\:-right-4:is(:where(.group)[data-side="left"] *) {
    right: calc(var(--spacing) * -4);
  }

  .group-data-\[side\=left\]\:border-r:is(:where(.group)[data-side="left"] *) {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .group-data-\[side\=right\]\:left-0:is(:where(.group)[data-side="right"] *) {
    left: calc(var(--spacing) * 0);
  }

  .group-data-\[side\=right\]\:rotate-180:is(:where(.group)[data-side="right"] *) {
    rotate: 180deg;
  }

  .group-data-\[side\=right\]\:border-l:is(:where(.group)[data-side="right"] *) {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .group-data-\[variant\=floating\]\:rounded-lg:is(:where(.group)[data-variant="floating"] *) {
    border-radius: var(--radius);
  }

  .group-data-\[variant\=floating\]\:border:is(:where(.group)[data-variant="floating"] *) {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .group-data-\[variant\=floating\]\:border-sidebar-border:is(:where(.group)[data-variant="floating"] *) {
    border-color: var(--sidebar-border);
  }

  .group-data-\[variant\=floating\]\:shadow-sm:is(:where(.group)[data-variant="floating"] *) {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  @media (hover: hover) {
    .peer-hover\/menu-button\:text-sidebar-accent-foreground:is(:where(.peer\/menu-button):hover ~ *) {
      color: var(--sidebar-accent-foreground);
    }
  }

  .peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
    cursor: not-allowed;
  }

  .peer-disabled\:opacity-50:is(:where(.peer):disabled ~ *) {
    opacity: .5;
  }

  .peer-data-\[active\=true\]\/menu-button\:text-sidebar-accent-foreground:is(:where(.peer\/menu-button)[data-active="true"] ~ *) {
    color: var(--sidebar-accent-foreground);
  }

  .peer-data-\[size\=default\]\/menu-button\:top-1\.5:is(:where(.peer\/menu-button)[data-size="default"] ~ *) {
    top: calc(var(--spacing) * 1.5);
  }

  .peer-data-\[size\=lg\]\/menu-button\:top-2\.5:is(:where(.peer\/menu-button)[data-size="lg"] ~ *) {
    top: calc(var(--spacing) * 2.5);
  }

  .peer-data-\[size\=sm\]\/menu-button\:top-1:is(:where(.peer\/menu-button)[data-size="sm"] ~ *) {
    top: calc(var(--spacing) * 1);
  }

  .selection\:bg-primary ::selection {
    background-color: var(--primary);
  }

  .selection\:bg-primary::selection {
    background-color: var(--primary);
  }

  .selection\:text-primary-foreground ::selection {
    color: var(--primary-foreground);
  }

  .selection\:text-primary-foreground::selection {
    color: var(--primary-foreground);
  }

  .file\:mr-4::file-selector-button {
    margin-right: calc(var(--spacing) * 4);
  }

  .file\:inline-flex::file-selector-button {
    display: inline-flex;
  }

  .file\:h-7::file-selector-button {
    height: calc(var(--spacing) * 7);
  }

  .file\:rounded-md::file-selector-button {
    border-radius: calc(var(--radius)  - 2px);
  }

  .file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:bg-blue-50::file-selector-button {
    background-color: var(--color-blue-50);
  }

  .file\:bg-transparent::file-selector-button {
    background-color: #0000;
  }

  .file\:px-4::file-selector-button {
    padding-inline: calc(var(--spacing) * 4);
  }

  .file\:py-2::file-selector-button {
    padding-block: calc(var(--spacing) * 2);
  }

  .file\:text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .file\:font-medium::file-selector-button {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .file\:text-blue-700::file-selector-button {
    color: var(--color-blue-700);
  }

  .file\:text-foreground::file-selector-button {
    color: var(--foreground);
  }

  .placeholder\:text-gray-500::placeholder {
    color: var(--color-gray-500);
  }

  .placeholder\:text-muted-foreground::placeholder {
    color: var(--muted-foreground);
  }

  .after\:absolute:after {
    content: var(--tw-content);
    position: absolute;
  }

  .after\:-inset-2:after {
    content: var(--tw-content);
    inset: calc(var(--spacing) * -2);
  }

  .after\:inset-y-0:after {
    content: var(--tw-content);
    inset-block: calc(var(--spacing) * 0);
  }

  .after\:left-1\/2:after {
    content: var(--tw-content);
    left: 50%;
  }

  .after\:w-\[2px\]:after {
    content: var(--tw-content);
    width: 2px;
  }

  .group-data-\[collapsible\=offcanvas\]\:after\:left-full:is(:where(.group)[data-collapsible="offcanvas"] *):after {
    content: var(--tw-content);
    left: 100%;
  }

  .first\:rounded-l-md:first-child {
    border-top-left-radius: calc(var(--radius)  - 2px);
    border-bottom-left-radius: calc(var(--radius)  - 2px);
  }

  .last\:rounded-r-md:last-child {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .last\:border-b-0:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .focus-within\:relative:focus-within {
    position: relative;
  }

  .focus-within\:z-20:focus-within {
    z-index: 20;
  }

  .focus-within\:ring-2:focus-within {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-within\:ring-ring:focus-within {
    --tw-ring-color: var(--ring);
  }

  .focus-within\:ring-offset-2:focus-within {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  @media (hover: hover) {
    .hover\:border-orange-400:hover {
      border-color: var(--color-orange-400);
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#974813\]:hover {
      background-color: #974813;
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#e8823d\]:hover {
      background-color: #e8823d;
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#ff914d\]:hover {
      background-color: #ff914d;
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[hsl\(var\(--accent\)\)\]:hover {
      background-color: hsl(var(--accent));
    }
  }

  @media (hover: hover) {
    .hover\:bg-accent:hover {
      background-color: var(--accent);
    }
  }

  @media (hover: hover) {
    .hover\:bg-black:hover {
      background-color: var(--color-black);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-50:hover {
      background-color: var(--color-blue-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-200:hover {
      background-color: var(--color-blue-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-destructive\/90:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-100:hover {
      background-color: var(--color-gray-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-800:hover {
      background-color: var(--color-gray-800);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-50:hover {
      background-color: var(--color-green-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-200:hover {
      background-color: var(--color-green-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-600:hover {
      background-color: var(--color-green-600);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-700:hover {
      background-color: var(--color-green-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted:hover {
      background-color: var(--muted);
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted\/50:hover {
      background-color: var(--muted);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-muted\/50:hover {
        background-color: color-mix(in oklab, var(--muted) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-orange-50:hover {
      background-color: var(--color-orange-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-orange-500:hover {
      background-color: var(--color-orange-500);
    }
  }

  @media (hover: hover) {
    .hover\:bg-orange-600:hover {
      background-color: var(--color-orange-600);
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-50:hover {
      background-color: var(--color-red-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-100:hover {
      background-color: var(--color-red-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-200:hover {
      background-color: var(--color-red-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-600:hover {
      background-color: var(--color-red-600);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-700:hover {
      background-color: var(--color-red-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-secondary\/80:hover {
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-secondary\/80:hover {
        background-color: color-mix(in oklab, var(--secondary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-sidebar-accent:hover {
      background-color: var(--sidebar-accent);
    }
  }

  @media (hover: hover) {
    .hover\:bg-slate-300:hover {
      background-color: var(--color-slate-300);
    }
  }

  @media (hover: hover) {
    .hover\:bg-yellow-200:hover {
      background-color: var(--color-yellow-200);
    }
  }

  @media (hover: hover) {
    .hover\:text-\[hsl\(var\(--accent-foreground\)\)\]:hover {
      color: hsl(var(--accent-foreground));
    }
  }

  @media (hover: hover) {
    .hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-800:hover {
      color: var(--color-blue-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-muted-foreground:hover {
      color: var(--muted-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-orange-700:hover {
      color: var(--color-orange-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-700:hover {
      color: var(--color-red-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-800:hover {
      color: var(--color-red-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-sidebar-accent-foreground:hover {
      color: var(--sidebar-accent-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-white:hover {
      color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-100:hover {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover {
      --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-md:hover {
      --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:group-data-\[collapsible\=offcanvas\]\:bg-sidebar:hover:is(:where(.group)[data-collapsible="offcanvas"] *) {
      background-color: var(--sidebar);
    }
  }

  @media (hover: hover) {
    .hover\:file\:bg-blue-100:hover::file-selector-button {
      background-color: var(--color-blue-100);
    }
  }

  @media (hover: hover) {
    .hover\:after\:bg-sidebar-border:hover:after {
      content: var(--tw-content);
      background-color: var(--sidebar-border);
    }
  }

  .focus\:z-10:focus {
    z-index: 10;
  }

  .focus\:border-black:focus {
    border-color: var(--color-black);
  }

  .focus\:border-blue-500:focus {
    border-color: var(--color-blue-500);
  }

  .focus\:border-primary:focus {
    border-color: var(--primary);
  }

  .focus\:bg-accent:focus {
    background-color: var(--accent);
  }

  .focus\:text-accent-foreground:focus {
    color: var(--accent-foreground);
  }

  .focus\:ring-0:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-black:focus {
    --tw-ring-color: var(--color-black);
  }

  .focus\:ring-blue-500:focus {
    --tw-ring-color: var(--color-blue-500);
  }

  .focus\:ring-orange-400:focus {
    --tw-ring-color: var(--color-orange-400);
  }

  .focus\:ring-ring:focus {
    --tw-ring-color: var(--ring);
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:outline-hidden:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .focus\:outline-hidden:focus {
      outline: 2px solid #0000;
      outline-offset: 2px;
    }
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:z-10:focus-visible {
    z-index: 10;
  }

  .focus-visible\:border-ring:focus-visible {
    border-color: var(--ring);
  }

  .focus-visible\:ring-1:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-2:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-\[3px\]:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-black:focus-visible {
    --tw-ring-color: var(--color-black);
  }

  .focus-visible\:ring-destructive\/20:focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-destructive\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .focus-visible\:ring-ring\/50:focus-visible {
    --tw-ring-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-ring\/50:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  .focus-visible\:outline-1:focus-visible {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .focus-visible\:outline-ring:focus-visible {
    outline-color: var(--ring);
  }

  .focus-visible\:outline-none:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  .active\:bg-sidebar-accent:active {
    background-color: var(--sidebar-accent);
  }

  .active\:text-sidebar-accent-foreground:active {
    color: var(--sidebar-accent-foreground);
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:bg-gray-400:disabled {
    background-color: var(--color-gray-400);
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  :where([data-side="left"]) .in-data-\[side\=left\]\:cursor-w-resize {
    cursor: w-resize;
  }

  :where([data-side="right"]) .in-data-\[side\=right\]\:cursor-e-resize {
    cursor: e-resize;
  }

  .has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\]:has([data-slot="card-action"]) {
    grid-template-columns: 1fr auto;
  }

  .has-data-\[variant\=inset\]\:bg-sidebar:has([data-variant="inset"]) {
    background-color: var(--sidebar);
  }

  .has-\[\>svg\]\:px-2\.5:has( > svg) {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .has-\[\>svg\]\:px-3:has( > svg) {
    padding-inline: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-4:has( > svg) {
    padding-inline: calc(var(--spacing) * 4);
  }

  .aria-disabled\:pointer-events-none[aria-disabled="true"] {
    pointer-events: none;
  }

  .aria-disabled\:opacity-50[aria-disabled="true"] {
    opacity: .5;
  }

  .aria-invalid\:border-destructive[aria-invalid="true"] {
    border-color: var(--destructive);
  }

  .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .data-\[active\=true\]\:bg-sidebar-accent[data-active="true"] {
    background-color: var(--sidebar-accent);
  }

  .data-\[active\=true\]\:font-medium[data-active="true"] {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .data-\[active\=true\]\:text-sidebar-accent-foreground[data-active="true"] {
    color: var(--sidebar-accent-foreground);
  }

  .data-\[disabled\]\:pointer-events-none[data-disabled] {
    pointer-events: none;
  }

  .data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: .5;
  }

  .data-\[error\=true\]\:text-destructive[data-error="true"] {
    color: var(--destructive);
  }

  .data-\[inset\]\:pl-8[data-inset] {
    padding-left: calc(var(--spacing) * 8);
  }

  .data-\[orientation\=horizontal\]\:h-px[data-orientation="horizontal"] {
    height: 1px;
  }

  .data-\[orientation\=horizontal\]\:w-full[data-orientation="horizontal"] {
    width: 100%;
  }

  .data-\[orientation\=vertical\]\:h-4[data-orientation="vertical"] {
    height: calc(var(--spacing) * 4);
  }

  .data-\[orientation\=vertical\]\:h-full[data-orientation="vertical"] {
    height: 100%;
  }

  .data-\[orientation\=vertical\]\:w-px[data-orientation="vertical"] {
    width: 1px;
  }

  .data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
    color: var(--muted-foreground);
  }

  .data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
    --tw-translate-y: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
    --tw-enter-translate-y: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=left\]\:-translate-x-1[data-side="left"] {
    --tw-translate-x: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
    --tw-enter-translate-x: calc(2 * var(--spacing));
  }

  .data-\[side\=right\]\:translate-x-1[data-side="right"] {
    --tw-translate-x: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
    --tw-enter-translate-x: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=top\]\:-translate-y-1[data-side="top"] {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
    --tw-enter-translate-y: calc(2 * var(--spacing));
  }

  .data-\[size\=default\]\:h-9[data-size="default"] {
    height: calc(var(--spacing) * 9);
  }

  .data-\[size\=sm\]\:h-8[data-size="sm"] {
    height: calc(var(--spacing) * 8);
  }

  :is(.\*\:data-\[slot\=select-value\]\:line-clamp-1 > *)[data-slot="select-value"] {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  :is(.\*\:data-\[slot\=select-value\]\:flex > *)[data-slot="select-value"] {
    display: flex;
  }

  :is(.\*\:data-\[slot\=select-value\]\:items-center > *)[data-slot="select-value"] {
    align-items: center;
  }

  :is(.\*\:data-\[slot\=select-value\]\:gap-2 > *)[data-slot="select-value"] {
    gap: calc(var(--spacing) * 2);
  }

  .data-\[slot\=sidebar-menu-button\]\:\!p-1\.5[data-slot="sidebar-menu-button"] {
    padding: calc(var(--spacing) * 1.5) !important;
  }

  .data-\[state\=active\]\:bg-background[data-state="active"] {
    background-color: var(--background);
  }

  .data-\[state\=active\]\:shadow-sm[data-state="active"] {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[state\=checked\]\:border-primary[data-state="checked"] {
    border-color: var(--primary);
  }

  .data-\[state\=checked\]\:bg-primary[data-state="checked"] {
    background-color: var(--primary);
  }

  .data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
    color: var(--primary-foreground);
  }

  .data-\[state\=closed\]\:animate-accordion-up[data-state="closed"] {
    animation: accordion-up var(--tw-duration, .2s) ease-out;
  }

  .data-\[state\=closed\]\:animate-out[data-state="closed"] {
    animation: exit var(--tw-duration, .15s) var(--tw-ease, ease);
  }

  .data-\[state\=closed\]\:duration-300[data-state="closed"] {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
    --tw-exit-opacity: 0;
  }

  .data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
    --tw-exit-translate-y: 100%;
  }

  .data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
    --tw-exit-translate-x: -100%;
  }

  .data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
    --tw-exit-translate-x: 100%;
  }

  .data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
    --tw-exit-translate-y: -100%;
  }

  .data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
    --tw-exit-scale: .95;
  }

  .data-\[state\=on\]\:bg-accent[data-state="on"] {
    background-color: var(--accent);
  }

  .data-\[state\=on\]\:text-accent-foreground[data-state="on"] {
    color: var(--accent-foreground);
  }

  .data-\[state\=open\]\:animate-accordion-down[data-state="open"] {
    animation: accordion-down var(--tw-duration, .2s) ease-out;
  }

  .data-\[state\=open\]\:animate-in[data-state="open"] {
    animation: enter var(--tw-duration, .15s) var(--tw-ease, ease);
  }

  .data-\[state\=open\]\:bg-accent[data-state="open"] {
    background-color: var(--accent);
  }

  .data-\[state\=open\]\:bg-secondary[data-state="open"] {
    background-color: var(--secondary);
  }

  .data-\[state\=open\]\:bg-sidebar-accent[data-state="open"] {
    background-color: var(--sidebar-accent);
  }

  .data-\[state\=open\]\:text-accent-foreground[data-state="open"] {
    color: var(--accent-foreground);
  }

  .data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
    color: var(--muted-foreground);
  }

  .data-\[state\=open\]\:text-sidebar-accent-foreground[data-state="open"] {
    color: var(--sidebar-accent-foreground);
  }

  .data-\[state\=open\]\:opacity-100[data-state="open"] {
    opacity: 1;
  }

  .data-\[state\=open\]\:duration-500[data-state="open"] {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .data-\[state\=open\]\:fade-in-0[data-state="open"] {
    --tw-enter-opacity: 0;
  }

  .data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
    --tw-enter-translate-y: 100%;
  }

  .data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
    --tw-enter-translate-x: -100%;
  }

  .data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
    --tw-enter-translate-x: 100%;
  }

  .data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
    --tw-enter-translate-y: -100%;
  }

  .data-\[state\=open\]\:zoom-in-95[data-state="open"] {
    --tw-enter-scale: .95;
  }

  @media (hover: hover) {
    .data-\[state\=open\]\:hover\:bg-sidebar-accent[data-state="open"]:hover {
      background-color: var(--sidebar-accent);
    }
  }

  @media (hover: hover) {
    .data-\[state\=open\]\:hover\:text-sidebar-accent-foreground[data-state="open"]:hover {
      color: var(--sidebar-accent-foreground);
    }
  }

  .data-\[state\=selected\]\:bg-muted[data-state="selected"] {
    background-color: var(--muted);
  }

  .data-\[variant\=destructive\]\:text-destructive[data-variant="destructive"] {
    color: var(--destructive);
  }

  .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
      background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
    }
  }

  .data-\[variant\=destructive\]\:focus\:text-destructive[data-variant="destructive"]:focus {
    color: var(--destructive);
  }

  .data-\[variant\=outline\]\:border-l-0[data-variant="outline"] {
    border-left-style: var(--tw-border-style);
    border-left-width: 0;
  }

  .data-\[variant\=outline\]\:shadow-xs[data-variant="outline"] {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[variant\=outline\]\:first\:border-l[data-variant="outline"]:first-child {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  @media (width >= 40rem) {
    .sm\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (width >= 40rem) {
    .sm\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 40rem) {
    .sm\:mb-8 {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:flex {
      display: flex;
    }
  }

  @media (width >= 40rem) {
    .sm\:hidden {
      display: none;
    }
  }

  @media (width >= 40rem) {
    .sm\:inline {
      display: inline;
    }
  }

  @media (width >= 40rem) {
    .sm\:w-64 {
      width: calc(var(--spacing) * 64);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-auto {
      width: auto;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-4xl {
      max-width: var(--container-4xl);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-\[425px\] {
      max-width: 425px;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-\[500px\] {
      max-width: 500px;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-\[700px\] {
      max-width: 700px;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-lg {
      max-width: var(--container-lg);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-sm {
      max-width: var(--container-sm);
    }
  }

  @media (width >= 40rem) {
    .sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 40rem) {
    .sm\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 40rem) {
    .sm\:items-center {
      align-items: center;
    }
  }

  @media (width >= 40rem) {
    .sm\:justify-between {
      justify-content: space-between;
    }
  }

  @media (width >= 40rem) {
    .sm\:justify-end {
      justify-content: flex-end;
    }
  }

  @media (width >= 40rem) {
    .sm\:p-4 {
      padding: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-3 {
      padding-inline: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-8 {
      padding-block: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:text-left {
      text-align: left;
    }
  }

  @media (width >= 40rem) {
    .sm\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-3 {
      grid-column: span 3 / span 3;
    }
  }

  @media (width >= 48rem) {
    .md\:block {
      display: block;
    }
  }

  @media (width >= 48rem) {
    .md\:flex {
      display: flex;
    }
  }

  @media (width >= 48rem) {
    .md\:hidden {
      display: none;
    }
  }

  @media (width >= 48rem) {
    .md\:w-32 {
      width: calc(var(--spacing) * 32);
    }
  }

  @media (width >= 48rem) {
    .md\:w-48 {
      width: calc(var(--spacing) * 48);
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 48rem) {
    .md\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:p-10 {
      padding: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 48rem) {
    .md\:py-6 {
      padding-block: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:opacity-0 {
      opacity: 0;
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:m-2:is(:where(.peer)[data-variant="inset"] ~ *) {
      margin: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:ml-0:is(:where(.peer)[data-variant="inset"] ~ *) {
      margin-left: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:rounded-xl:is(:where(.peer)[data-variant="inset"] ~ *) {
      border-radius: calc(var(--radius)  + 4px);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:shadow-sm:is(:where(.peer)[data-variant="inset"] ~ *) {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:peer-data-\[state\=collapsed\]\:ml-2:is(:where(.peer)[data-variant="inset"] ~ *):is(:where(.peer)[data-state="collapsed"] ~ *) {
      margin-left: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 48rem) {
    .md\:after\:hidden:after {
      content: var(--tw-content);
      display: none;
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-2 {
      gap: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 64rem) {
    .lg\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 64rem) {
    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  :where(.dark\:divide-gray-700:is(.dark *) > :not(:last-child)) {
    border-color: var(--color-gray-700);
  }

  .dark\:border-gray-600:is(.dark *) {
    border-color: var(--color-gray-600);
  }

  .dark\:border-gray-700:is(.dark *) {
    border-color: var(--color-gray-700);
  }

  .dark\:border-input:is(.dark *) {
    border-color: var(--input);
  }

  .dark\:border-white:is(.dark *) {
    border-color: var(--color-white);
  }

  .dark\:bg-black:is(.dark *) {
    background-color: var(--color-black);
  }

  .dark\:bg-destructive\/60:is(.dark *) {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-destructive\/60:is(.dark *) {
      background-color: color-mix(in oklab, var(--destructive) 60%, transparent);
    }
  }

  .dark\:bg-gray-700:is(.dark *) {
    background-color: var(--color-gray-700);
  }

  .dark\:bg-gray-800:is(.dark *) {
    background-color: var(--color-gray-800);
  }

  .dark\:bg-gray-900:is(.dark *) {
    background-color: var(--color-gray-900);
  }

  .dark\:bg-green-900:is(.dark *) {
    background-color: var(--color-green-900);
  }

  .dark\:bg-input\/30:is(.dark *) {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-input\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
    }
  }

  .dark\:bg-orange-900:is(.dark *) {
    background-color: var(--color-orange-900);
  }

  .dark\:bg-red-900:is(.dark *) {
    background-color: var(--color-red-900);
  }

  .dark\:bg-white:is(.dark *) {
    background-color: var(--color-white);
  }

  .dark\:bg-yellow-900:is(.dark *) {
    background-color: var(--color-yellow-900);
  }

  .dark\:from-gray-800:is(.dark *) {
    --tw-gradient-from: var(--color-gray-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:to-gray-900:is(.dark *) {
    --tw-gradient-to: var(--color-gray-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .dark\:text-black:is(.dark *) {
    color: var(--color-black);
  }

  .dark\:text-gray-300:is(.dark *) {
    color: var(--color-gray-300);
  }

  .dark\:text-gray-400:is(.dark *) {
    color: var(--color-gray-400);
  }

  .dark\:text-gray-500:is(.dark *) {
    color: var(--color-gray-500);
  }

  .dark\:text-gray-700:is(.dark *) {
    color: var(--color-gray-700);
  }

  .dark\:text-green-200:is(.dark *) {
    color: var(--color-green-200);
  }

  .dark\:text-muted-foreground:is(.dark *) {
    color: var(--muted-foreground);
  }

  .dark\:text-orange-200:is(.dark *) {
    color: var(--color-orange-200);
  }

  .dark\:text-orange-400:is(.dark *) {
    color: var(--color-orange-400);
  }

  .dark\:text-red-200:is(.dark *) {
    color: var(--color-red-200);
  }

  .dark\:text-white:is(.dark *) {
    color: var(--color-white);
  }

  .dark\:text-yellow-200:is(.dark *) {
    color: var(--color-yellow-200);
  }

  .dark\:placeholder\:text-gray-400:is(.dark *)::placeholder {
    color: var(--color-gray-400);
  }

  @media (hover: hover) {
    .dark\:hover\:bg-accent\/50:is(.dark *):hover {
      background-color: var(--accent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-accent\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--accent) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-gray-800:is(.dark *):hover {
      background-color: var(--color-gray-800);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-gray-900:is(.dark *):hover {
      background-color: var(--color-gray-900);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-input\/50:is(.dark *):hover {
      background-color: var(--input);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-input\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--input) 50%, transparent);
      }
    }
  }

  .dark\:focus\:border-white:is(.dark *):focus {
    border-color: var(--color-white);
  }

  .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:data-\[state\=active\]\:border-input:is(.dark *)[data-state="active"] {
    border-color: var(--input);
  }

  .dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state="active"] {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state="active"] {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
    }
  }

  .dark\:data-\[state\=active\]\:text-foreground:is(.dark *)[data-state="active"] {
    color: var(--foreground);
  }

  .dark\:data-\[state\=checked\]\:bg-primary:is(.dark *)[data-state="checked"] {
    background-color: var(--primary);
  }

  .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
      background-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .recharts-cartesian-axis-tick text {
    fill: var(--muted-foreground);
  }

  .\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"] {
    stroke: var(--border);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"] {
      stroke: color-mix(in oklab, var(--border) 50%, transparent);
    }
  }

  .\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-curve.recharts-tooltip-cursor {
    stroke: var(--border);
  }

  .\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-dot[stroke="#fff"] {
    stroke: #0000;
  }

  .\[\&_\.recharts-layer\]\:outline-hidden .recharts-layer {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .\[\&_\.recharts-layer\]\:outline-hidden .recharts-layer {
      outline: 2px solid #0000;
      outline-offset: 2px;
    }
  }

  .\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-polar-grid [stroke="#ccc"] {
    stroke: var(--border);
  }

  .\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radial-bar-background-sector {
    fill: var(--muted);
  }

  .\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted .recharts-rectangle.recharts-tooltip-cursor {
    fill: var(--muted);
  }

  .\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-reference-line [stroke="#ccc"] {
    stroke: var(--border);
  }

  .\[\&_\.recharts-sector\]\:outline-hidden .recharts-sector {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .\[\&_\.recharts-sector\]\:outline-hidden .recharts-sector {
      outline: 2px solid #0000;
      outline-offset: 2px;
    }
  }

  .\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-sector[stroke="#fff"] {
    stroke: #0000;
  }

  .\[\&_\.recharts-surface\]\:outline-hidden .recharts-surface {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .\[\&_\.recharts-surface\]\:outline-hidden .recharts-surface {
      outline: 2px solid #0000;
      outline-offset: 2px;
    }
  }

  .\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
  }

  .\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
  }

  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*="text-"]) {
    color: var(--muted-foreground);
  }

  .\[\&_tr\]\:border-b tr {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .\[\&_tr\:last-child\]\:border-0 tr:last-child {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role="checkbox"]) {
    padding-right: calc(var(--spacing) * 0);
  }

  .\[\.border-b\]\:pb-6.border-b {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .\[\.border-t\]\:pt-6.border-t {
    padding-top: calc(var(--spacing) * 6);
  }

  :is(.\*\:\[span\]\:last\:flex > *):is(span):last-child {
    display: flex;
  }

  :is(.\*\:\[span\]\:last\:items-center > *):is(span):last-child {
    align-items: center;
  }

  :is(.\*\:\[span\]\:last\:gap-2 > *):is(span):last-child {
    gap: calc(var(--spacing) * 2);
  }

  :is(.data-\[variant\=destructive\]\:\*\:\[svg\]\:\!text-destructive[data-variant="destructive"] > *):is(svg) {
    color: var(--destructive) !important;
  }

  .\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\] > [role="checkbox"] {
    --tw-translate-y: 2px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .\[\&\>button\]\:hidden > button {
    display: none;
  }

  .\[\&\>span\:last-child\]\:truncate > span:last-child {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .\[\&\>svg\]\:pointer-events-none > svg {
    pointer-events: none;
  }

  .\[\&\>svg\]\:size-3 > svg {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:size-4 > svg {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&\>svg\]\:h-2\.5 > svg {
    height: calc(var(--spacing) * 2.5);
  }

  .\[\&\>svg\]\:h-3 > svg {
    height: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:w-2\.5 > svg {
    width: calc(var(--spacing) * 2.5);
  }

  .\[\&\>svg\]\:w-3 > svg {
    width: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:shrink-0 > svg {
    flex-shrink: 0;
  }

  .\[\&\>svg\]\:text-muted-foreground > svg {
    color: var(--muted-foreground);
  }

  .\[\&\>svg\]\:text-sidebar-accent-foreground > svg {
    color: var(--sidebar-accent-foreground);
  }

  .\[\&\>tr\]\:last\:border-b-0 > tr:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state="open"] > svg {
    rotate: 180deg;
  }

  [data-side="left"][data-collapsible="offcanvas"] .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2 {
    right: calc(var(--spacing) * -2);
  }

  [data-side="left"][data-state="collapsed"] .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize {
    cursor: e-resize;
  }

  [data-side="right"][data-collapsible="offcanvas"] .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2 {
    left: calc(var(--spacing) * -2);
  }

  [data-side="right"][data-state="collapsed"] .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize {
    cursor: w-resize;
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-accent:hover {
      background-color: var(--accent);
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-destructive\/90:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-secondary\/90:hover {
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-secondary\/90:hover {
        background-color: color-mix(in oklab, var(--secondary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
    }
  }
}

:root {
  --radius: .625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(.13 .028 261.692);
  --card: oklch(1 0 0);
  --card-foreground: oklch(.13 .028 261.692);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(.13 .028 261.692);
  --primary: oklch(.21 .034 264.665);
  --primary-foreground: oklch(.985 .002 247.839);
  --secondary: oklch(.967 .003 264.542);
  --secondary-foreground: oklch(.21 .034 264.665);
  --muted: oklch(.967 .003 264.542);
  --muted-foreground: oklch(.551 .027 264.364);
  --accent: oklch(.967 .003 264.542);
  --accent-foreground: oklch(.21 .034 264.665);
  --destructive: oklch(.577 .245 27.325);
  --border: oklch(.928 .006 264.531);
  --input: oklch(.928 .006 264.531);
  --ring: oklch(.707 .022 261.325);
  --chart-1: oklch(.646 .222 41.116);
  --chart-2: oklch(.6 .118 184.704);
  --chart-3: oklch(.398 .07 227.392);
  --chart-4: oklch(.828 .189 84.429);
  --chart-5: oklch(.769 .188 70.08);
  --sidebar: oklch(.985 .002 247.839);
  --sidebar-foreground: oklch(.13 .028 261.692);
  --sidebar-primary: oklch(.21 .034 264.665);
  --sidebar-primary-foreground: oklch(.985 .002 247.839);
  --sidebar-accent: oklch(.967 .003 264.542);
  --sidebar-accent-foreground: oklch(.21 .034 264.665);
  --sidebar-border: oklch(.928 .006 264.531);
  --sidebar-ring: oklch(.707 .022 261.325);
}

.dark {
  --background: oklch(.13 .028 261.692);
  --foreground: oklch(.985 .002 247.839);
  --card: oklch(.21 .034 264.665);
  --card-foreground: oklch(.985 .002 247.839);
  --popover: oklch(.21 .034 264.665);
  --popover-foreground: oklch(.985 .002 247.839);
  --primary: oklch(.928 .006 264.531);
  --primary-foreground: oklch(.21 .034 264.665);
  --secondary: oklch(.278 .033 256.848);
  --secondary-foreground: oklch(.985 .002 247.839);
  --muted: oklch(.278 .033 256.848);
  --muted-foreground: oklch(.707 .022 261.325);
  --accent: oklch(.278 .033 256.848);
  --accent-foreground: oklch(.985 .002 247.839);
  --destructive: oklch(.704 .191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(.551 .027 264.364);
  --chart-1: oklch(.488 .243 264.376);
  --chart-2: oklch(.696 .17 162.48);
  --chart-3: oklch(.769 .188 70.08);
  --chart-4: oklch(.627 .265 303.9);
  --chart-5: oklch(.645 .246 16.439);
  --sidebar: oklch(.21 .034 264.665);
  --sidebar-foreground: oklch(.985 .002 247.839);
  --sidebar-primary: oklch(.488 .243 264.376);
  --sidebar-primary-foreground: oklch(.985 .002 247.839);
  --sidebar-accent: oklch(.278 .033 256.848);
  --sidebar-accent-foreground: oklch(.985 .002 247.839);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(.551 .027 264.364);
}

@layer base {
  * {
    border-color: var(--border);
    outline-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    * {
      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
  }
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
  initial-value: rotateX(0);
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
  initial-value: rotateY(0);
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
  initial-value: rotateZ(0);
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false;
  initial-value: skewX(0);
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false;
  initial-value: skewY(0);
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-ordinal {
  syntax: "*";
  inherits: false
}

@property --tw-slashed-zero {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-figure {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}

@keyframes accordion-down {
  from {
    height: 0;
  }

  to {
    height: var(--radix-accordion-content-height, var(--bits-accordion-content-height));
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height, var(--bits-accordion-content-height));
  }

  to {
    height: 0;
  }
}

@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: rotateX(0);
      --tw-rotate-y: rotateY(0);
      --tw-rotate-z: rotateZ(0);
      --tw-skew-x: skewX(0);
      --tw-skew-y: skewY(0);
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
    }
  }
}

/*# sourceMappingURL=src_app_globals_b52d8e88.css.map*/