@include('auth.header')

<body class="hold-transition login-page">
    <div class="container-fluid">
        <div class="login d-flex align-items-center py-5">
            <div class="container">
                <div class="row">
                    <div class="col-lg-4 col-xl-4 mx-auto">
                        <div class="content text-center">
                            <img src="{{ $tenantLogo }}" alt="logo" class="mb-4" style="width:170px;" />
                            <p class="text-muted mb-4">Sign in to your class account</p>
                        </div>

                        <form id="classOtpLoginForm">
                            <div class="form-group mb-3">
                                <input type="text" id="class_mobile_input" placeholder="Enter mobile number"
                                    class="form-control shadow-sm px-4" maxlength="10">
                                <div id="class-mobile-error" class="text-danger small d-none mt-1">Invalid mobile</div>
                            </div>

                            <div class="form-group mb-3 d-none" id="class_otp_wrapper">
                                <input type="text" id="class_otp_input" placeholder="Enter OTP"
                                    class="form-control shadow-sm px-4" maxlength="6">
                                <div id="class-otp-error" class="text-danger small d-none mt-1">Invalid OTP</div>
                            </div>

                            <button type="button" id="classSendOtpBtn"
                                class="btn btn-primary btn-block text-uppercase mb-2 shadow-sm">Send OTP</button>

                            <button type="button" id="classVerifyOtpBtn"
                                class="btn btn-success btn-block text-uppercase mb-2 shadow-sm d-none">Verify OTP</button>
                        </form>

                        <div class="text-center mt-3">
                            <p class="text-muted small">
                                <strong>Copyright ©
                                    <a href="https://www.uest.in/">Uest</a>.</strong> All rights reserved.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="{{ asset('plugins/jquery/jquery.min.js') }}"></script>
    <script src="{{ asset('plugins/toastr/toastr.min.js') }}"></script>

    <script>
        $('#classSendOtpBtn').on('click', function () {
            const contactNo = $('#class_mobile_input').val().trim();
            $('#class-mobile-error').addClass('d-none');

            if (!/^[6-9]\d{9}$/.test(contactNo)) {
                $('#class-mobile-error').removeClass('d-none').text('Please enter a valid 10-digit mobile number');
                return;
            }


            $.ajax({
                url: "{{ route('checkClassByMobile') }}", // backend route for Classes
                type: "POST",
                data: {
                    contact_no: contactNo,
                    _token: '{{ csrf_token() }}'
                },
                success: function (res) {
                    if (res.success) {
                        toastr.success("OTP sent to your number.");
                        $('#class_otp_wrapper').removeClass('d-none');
                        $('#classVerifyOtpBtn').removeClass('d-none');
                        $('#class_otp_input').val('').focus();
                        disableClassOtpButton();
                    } else {
                        toastr.error(res.message || 'Unable to send OTP.');
                    }
                },
                error: function () {
                    toastr.error("Something went wrong.");
                }
            });
        });

        $('#classVerifyOtpBtn').on('click', function () {
            const contactNo = $('#class_mobile_input').val().trim();
            const otp = $('#class_otp_input').val().trim();
            $('#class-otp-error').addClass('d-none');

            if (!/^\d{6}$/.test(otp)) {
                $('#class-otp-error').removeClass('d-none').text('Enter valid 6-digit OTP');
                return;
            }

            $.ajax({
                url: "{{ route('verifyClassOtp') }}",
                type: "POST",
                data: {
                    contact_no: contactNo,
                    otp: otp,
                    _token: '{{ csrf_token() }}'
                },
                success: function (res) {
                    if (res.success) {
                        toastr.success("Logged in successfully.");
                        window.location.href = "{{ route('home') }}";
                    } else {
                        $('#class-otp-error').removeClass('d-none').text(res.message || 'Invalid OTP');
                    }
                },
                error: function () {
                    toastr.error("Failed to verify OTP.");
                }
            });
        });

        function disableClassOtpButton() {
            const $btn = $('#classSendOtpBtn');
            let countdown = 60;
            $btn.prop('disabled', true).text(`Wait (${countdown}s)`);

            const interval = setInterval(() => {
                countdown--;
                $btn.text(`Wait (${countdown}s)`);

                if (countdown <= 0) {
                    clearInterval(interval);
                    $btn.prop('disabled', false).text('Send OTP');
                }
            }, 1000);
        }
    </script>
</body>
</html>