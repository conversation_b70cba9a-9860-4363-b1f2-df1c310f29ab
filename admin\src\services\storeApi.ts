import { axiosInstance } from '@/lib/axios';

export interface StoreItem {
  id: string;
  name: string;
  description: string;
  price: number;
  coinPrice: number;
  quantity: number;
  category: string;
  image: string | null;
  status: 'ACTIVE' | 'INACTIVE';
  createdAt: string;
  updatedAt: string;
}

export interface CreateStoreItemData {
  name: string;
  description: string;
  price: number;
  coinPrice: number;
  quantity: number;
  category: string;
  image?: string;
}

export interface UpdateStoreItemData {
  name?: string;
  description?: string;
  price?: number;
  coinPrice?: number;
  quantity?: number;
  category?: string;
  image?: string;
  status?: 'ACTIVE' | 'INACTIVE';
}

export interface StoreFilters {
  category?: string;
  status?: string;
  search?: string;
}

export interface StoreStats {
  totalItems: number;
  activeItems: number;
  inactiveItems: number;
  outOfStockItems: number;
  categoriesCount: number;
  categories: Array<{
    category: string;
    count: number;
  }>;
}

// Get all store items with optional filters
export const getAllStoreItems = async (filters?: StoreFilters): Promise<StoreItem[]> => {
  try {
    const params = new URLSearchParams();
    if (filters?.category) params.append('category', filters.category);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.search) params.append('search', filters.search);

    console.log('Fetching store items with URL:', `/admin/store?${params.toString()}`);
    const response = await axiosInstance.get(`/admin/store?${params.toString()}`);
    console.log('Store items response:', response.data);
    return response.data.data;
  } catch (error: any) {
    console.error('Store items fetch error:', error.response || error);
    throw new Error(error.response?.data?.message || 'Failed to fetch store items');
  }
};

// Get store item by ID
export const getStoreItemById = async (id: string): Promise<StoreItem> => {
  try {
    const response = await axiosInstance.get(`/admin/store/${id}`);
    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch store item');
  }
};

// Create new store item
export const createStoreItem = async (data: CreateStoreItemData): Promise<StoreItem> => {
  try {
    console.log('Creating store item with data:', data);
    const response = await axiosInstance.post('/admin/store', data);
    console.log('Create store item response:', response.data);
    return response.data.data;
  } catch (error: any) {
    console.error('Create store item error:', error.response || error);
    throw new Error(error.response?.data?.message || 'Failed to create store item');
  }
};

// Update store item
export const updateStoreItem = async (id: string, data: UpdateStoreItemData): Promise<StoreItem> => {
  try {
    const response = await axiosInstance.put(`/admin/store/${id}`, data);
    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to update store item');
  }
};

// Delete store item
export const deleteStoreItem = async (id: string): Promise<void> => {
  try {
    await axiosInstance.delete(`/admin/store/${id}`);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to delete store item');
  }
};

// Get store statistics
export const getStoreStats = async (): Promise<StoreStats> => {
  try {
    const response = await axiosInstance.get('/admin/store/stats');
    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch store statistics');
  }
};
